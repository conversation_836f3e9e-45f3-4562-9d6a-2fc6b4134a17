# Epic: Integração Cakto Member - Webhook de Vendas e Sincronização de Produtores

## 🎯 Epic Description
Como **sistema Cakto**, quero **integrar automaticamente com o Cakto Member** para que **produtores possam entregar seus cursos na plataforma de membros** e **clientes tenham acesso automático aos cursos comprados**.

**Domínio:** `aluno.cakto.com.br`

---

## 📋 User Stories

### US-001: Webhook de Vendas Aprovadas
**Como** sistema de pagamentos do Cakto
**Quero** enviar webhook para o Cakto Member quando uma venda for aprovada
**Para que** o cliente tenha acesso automático ao curso comprado

#### 📝 Acceptance Criteria
- [ ] Webhook é enviado para `aluno.cakto.com.br/api/webhooks/cakto/purchase` quando venda é aprovada
- [ ] Webhook só é enviado para produtos com `contentDelivery = 'cakto'`
- [ ] Payload inclui dados completos: cliente, produto, transação, pagamento
- [ ] Autenticação HMAC é implementada para segurança
- [ ] Cliente é criado automaticamente no Cakto Member se não existir
- [ ] Acesso ao curso é concedido automaticamente
- [ ] Email de boas-vindas é enviado para novos clientes

#### 🔧 Technical Details
```python
# cakto-backend/financial/utils.py
@job
def send_purchase_webhook_to_members(order: Order):
    # Verificar se produto usa Cakto Member
    if order.product.contentDelivery != 'cakto':
        return

    # Enviar webhook para aluno.cakto.com.br
    # Payload completo com dados da transação
```

#### 🧪 Test Scenarios
- [ ] Venda aprovada → Cliente criado no Cakto Member
- [ ] Venda aprovada → Acesso ao curso concedido
- [ ] Cliente já existe → Apenas acesso concedido
- [ ] Produto não usa Cakto Member → Webhook não enviado
- [ ] Webhook falha → Retry automático

---

### US-002: Sincronização de Produtores
**Como** produtor do Cakto
**Quero** ser criado automaticamente no Cakto Member quando escolher "Área de membros Cakto"
**Para que** possa gerenciar meus cursos na plataforma de membros

#### 📝 Acceptance Criteria
- [ ] Produtor é criado no Cakto Member quando escolhe `contentDelivery = 'cakto'`
- [ ] Dados do produtor são sincronizados: nome, email, telefone, documento
- [ ] Produtor recebe acesso de administrador no Cakto Member
- [ ] Campo `membersV2Id` é atualizado no produto do Cakto
- [ ] Produtor pode acessar `aluno.cakto.com.br` com suas credenciais do Cakto

#### 🔧 Technical Details
```python
# cakto-backend/product/services.py
@job
def sync_producer_to_members(user: User, product: Product):
    # Criar/atualizar produtor no Cakto Member
    # Retornar courseId para atualizar membersV2Id
```

#### 🧪 Test Scenarios
- [ ] Criar produto com Cakto Member → Produtor criado
- [ ] Atualizar produto para Cakto Member → Produtor sincronizado
- [ ] Produtor já existe → Apenas atualização
- [ ] Falha na sincronização → Log de erro

---

### US-003: Configuração de Ambiente
**Como** DevOps
**Quero** configurar as variáveis de ambiente necessárias
**Para que** a integração funcione corretamente

#### 📝 Acceptance Criteria
- [ ] Variáveis configuradas no ambiente de produção
- [ ] Variáveis configuradas no ambiente de staging
- [ ] Documentação das variáveis criada
- [ ] Testes de conectividade implementados

#### 🔧 Environment Variables
```bash
# cakto-backend/.env
MEMBERS_WEBHOOK_URL=https://aluno.cakto.com.br/api/webhooks/cakto/purchase
MEMBERS_WEBHOOK_SECRET=your_webhook_secret_here
MEMBERS_API_URL=https://aluno.cakto.com.br
MEMBERS_API_SECRET=your_api_secret_here
```

---

## 🚀 Implementation Plan

### Sprint 1: Webhook Foundation
**Duration:** 3 days
**Team:** Backend

#### Tasks
- [ ] **TASK-001:** Expandir função `send_purchase_webhook_to_members`
- [ ] **TASK-002:** Implementar payload completo do webhook
- [ ] **TASK-003:** Adicionar validação de `contentDelivery = 'cakto'`
- [ ] **TASK-004:** Implementar autenticação HMAC
- [ ] **TASK-005:** Configurar variáveis de ambiente
- [ ] **TASK-006:** Implementar testes unitários

#### Definition of Done
- [ ] Webhook envia payload correto para `aluno.cakto.com.br`
- [ ] Autenticação HMAC funciona
- [ ] Testes passam (cobertura > 80%)
- [ ] Logs de erro implementados
- [ ] Documentação técnica atualizada

---

### Sprint 2: Producer Sync
**Duration:** 3 days
**Team:** Backend

#### Tasks
- [ ] **TASK-007:** Criar função `sync_producer_to_members`
- [ ] **TASK-008:** Integrar na criação de produto
- [ ] **TASK-009:** Integrar na atualização de produto
- [ ] **TASK-010:** Implementar atualização de `membersV2Id`
- [ ] **TASK-011:** Implementar testes de integração
- [ ] **TASK-012:** Criar endpoint `/api/webhooks/cakto/producer` no Cakto Member

#### Definition of Done
- [ ] Produtor é criado no Cakto Member automaticamente
- [ ] `membersV2Id` é atualizado corretamente
- [ ] Testes de integração passam
- [ ] Endpoint do Cakto Member está funcionando
- [ ] Logs de sucesso/erro implementados

---

### Sprint 3: Integration & Testing
**Duration:** 2 days
**Team:** Backend + QA

#### Tasks
- [ ] **TASK-013:** Testes end-to-end completos
- [ ] **TASK-014:** Testes de cenários de erro
- [ ] **TASK-015:** Monitoramento e alertas
- [ ] **TASK-016:** Documentação para usuários
- [ ] **TASK-017:** Deploy em staging
- [ ] **TASK-018:** Validação com stakeholders

#### Definition of Done
- [ ] Fluxo completo funciona: Produto → Venda → Cliente → Curso
- [ ] Todos os cenários de erro são tratados
- [ ] Monitoramento está ativo
- [ ] Documentação está completa
- [ ] Stakeholders aprovaram a funcionalidade

---

## 🔧 Technical Architecture

### Webhook Flow
```
Cakto Backend → Webhook → Cakto Member (aluno.cakto.com.br)
     ↓              ↓              ↓
  Venda Aprovada → Payload → Cliente + Curso
```

### Producer Sync Flow
```
Cakto Frontend → Cakto Backend → Cakto Member (aluno.cakto.com.br)
     ↓              ↓              ↓
  Criar Produto → Sync Job → Produtor + Curso
```

### Security
- **HMAC Authentication:** Todos os webhooks
- **HTTPS Only:** Comunicação segura
- **Rate Limiting:** Proteção contra spam
- **Input Validation:** Validação de payload

---

## 📊 Success Metrics

### Business Metrics
- **Conversão:** % de clientes que acessam o curso após compra
- **Engajamento:** Tempo médio no curso
- **Satisfação:** NPS dos produtores

### Technical Metrics
- **Webhook Success Rate:** > 99%
- **Response Time:** < 2s
- **Error Rate:** < 1%
- **Uptime:** > 99.9%

---

## 🚨 Risk Assessment

### High Risk
- **Webhook Failures:** Pode impedir acesso aos cursos
- **Mitigation:** Retry automático + fallback manual

### Medium Risk
- **Producer Sync Failures:** Produtor não consegue gerenciar cursos
- **Mitigation:** Notificação + processo manual

### Low Risk
- **Performance Impact:** Webhooks podem afetar performance
- **Mitigation:** Jobs assíncronos + monitoramento

---

## 📝 Dependencies

### Internal Dependencies
- [ ] Cakto Member (`aluno.cakto.com.br`) deve estar funcionando
- [ ] Endpoint `/api/webhooks/cakto/purchase` deve estar implementado
- [ ] Endpoint `/api/webhooks/cakto/producer` deve ser criado

### External Dependencies
- [ ] DNS configurado para `aluno.cakto.com.br`
- [ ] SSL certificado para `aluno.cakto.com.br`
- [ ] Variáveis de ambiente configuradas

---

## 🎯 Definition of Ready

### Business Requirements
- [ ] Stakeholders definiram o fluxo desejado
- [ ] Produtores foram consultados sobre necessidades
- [ ] ROI foi calculado e aprovado

### Technical Requirements
- [ ] Arquitetura foi definida e aprovada
- [ ] APIs do Cakto Member estão documentadas
- [ ] Ambiente de desenvolvimento está configurado
- [ ] Equipe tem conhecimento necessário

### Quality Requirements
- [ ] Critérios de aceitação estão claros
- [ ] Cenários de teste foram definidos
- [ ] Métricas de sucesso foram estabelecidas

---

## 🔗 Related Issues

### Dependencies
- **Cakto Member Setup:** Configuração inicial da plataforma
- **DNS Configuration:** Configuração do domínio `aluno.cakto.com.br`
- **SSL Certificate:** Certificado para `aluno.cakto.com.br`

### Related Features
- **US-004:** Dashboard do produtor no Cakto Member
- **US-005:** Relatórios de vendas integrados
- **US-006:** Notificações automáticas

---

## 📞 Stakeholders

### Product Owner
- **Name:** [Nome do PO]
- **Email:** [<EMAIL>]
- **Role:** Definição de prioridades e critérios de aceitação

### Technical Lead
- **Name:** [Nome do Tech Lead]
- **Email:** [<EMAIL>]
- **Role:** Revisão técnica e arquitetura

### DevOps
- **Name:** [Nome do DevOps]
- **Email:** [<EMAIL>]
- **Role:** Configuração de ambiente e deploy

### QA Lead
- **Name:** [Nome do QA]
- **Email:** [<EMAIL>]
- **Role:** Estratégia de testes e validação

---

## 📅 Timeline

### Week 1: Foundation
- **Days 1-3:** Sprint 1 - Webhook Foundation
- **Day 4:** Code Review e ajustes
- **Day 5:** Deploy em staging

### Week 2: Producer Sync
- **Days 1-3:** Sprint 2 - Producer Sync
- **Day 4:** Code Review e ajustes
- **Day 5:** Deploy em staging

### Week 3: Integration
- **Days 1-2:** Sprint 3 - Integration & Testing
- **Day 3:** Validação com stakeholders
- **Day 4:** Deploy em produção
- **Day 5:** Monitoramento e ajustes

**Total:** 3 semanas (15 dias úteis)

---

## 🏷️ Labels

- `epic`
- `backend`
- `integration`
- `webhook`
- `cakto-member`
- `high-priority`
- `revenue-impact`

---

## 📋 Checklist Final

### Before Development
- [ ] Epic aprovada pelo Product Owner
- [ ] Arquitetura revisada pelo Tech Lead
- [ ] Ambiente de desenvolvimento configurado
- [ ] Equipe alocada e treinada

### During Development
- [ ] Daily standups com progresso
- [ ] Code reviews em cada PR
- [ ] Testes automatizados passando
- [ ] Documentação sendo atualizada

### Before Production
- [ ] Todos os testes passando
- [ ] Performance validada
- [ ] Segurança auditada
- [ ] Stakeholders aprovam
- [ ] Rollback plan definido

### After Production
- [ ] Monitoramento ativo
- [ ] Métricas sendo coletadas
- [ ] Feedback dos usuários
- [ ] Ajustes baseados em dados
