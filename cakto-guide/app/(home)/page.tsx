import Link from "next/link";

const modules = [
  {
    title: "Discovery Técnico",
    description:
      "Pix Automático é um módulo que permite a criação de cobranças recorrentes via Pix, facilitando o gerenciamento de pagamentos regulares.",
    href: "/docs/discovery",
  },
  {
    title: "Manual de Desenvolvimento",
    description:
      "Guia completo para desenvolvedores e tech leads com todas as regras de negócio, fluxos, APIs e boas práticas para desenvolvimento e manutenção do sistema.",
    href: "/docs/manual",
  },
  {
    title: "Pix Automático",
    description:
      "Pix Automático é um módulo que permite a criação de cobranças recorrentes via Pix, facilitando o gerenciamento de pagamentos regulares.",
    href: "/docs/pix_recurrency",
  },
];

export default function HomePage() {
  return (
    <div className="w-full max-w-5xl mx-auto mt-10">
      <h1 className="text-3xl font-bold text-center mb-4">
        Bem-vindo à Documentação
      </h1>
      <p className="text-center text-muted-foreground mb-8 max-w-2xl mx-auto">
        Explore os principais módulos da plataforma Cakto. Clique em um card
        para acessar a documentação detalhada de cada área.
      </p>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.map((mod) => (
          <Link
            key={mod.href}
            href={mod.href}
            className="rounded-xl border border-border bg-background shadow-sm hover:shadow-md transition-shadow p-6 flex flex-col gap-2 hover:border-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <h2 className="text-lg font-semibold text-green-700 dark:text-green-400 mb-1">
              {mod.title}
            </h2>
            <p className="text-muted-foreground text-sm flex-1">
              {mod.description}
            </p>
            <span className="mt-2 text-green-600 dark:text-green-400 font-medium text-sm">
              Acessar &rarr;
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
}
