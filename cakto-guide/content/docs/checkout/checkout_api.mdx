---
title: 'API de Criação de Pagamentos'
description: 'API para processamento de pagamentos com suporte a múltiplos métodos, incluindo cartões de crédito, 3D Secure, Google Pay, Apple Pay e assinaturas.'
---

# API de Criação de Pagamentos
Esta API permite o **processamento de diversos métodos de pagamento**, incluindo cartões de crédito, 3D Secure, Google Pay, Apple Pay e assinaturas recorrentes.
Ao realizar a chamada, o sistema valida os dados do usuário, verifica permissões, processa o pagamento através da estratégia apropriada e retorna uma resposta detalhada. A API suporta pagamentos únicos e recorrentes, além de divisão de valores entre produtor, co-produtores e afiliados.
Esta solução oferece uma interface unificada para processamento de pagamentos, com validação e tratamento de erros.
# Funcionamento Interno da API
A seguir, um diagrama simplificado do fluxo de execução para criação de pagamentos:

```mermaid
sequenceDiagram
    participant Client
    participant "PaymentCreateView (API)"
    participant "PaymentCreateSerializer"
    participant "to_payment_model()"
    participant "PaymentStrategyFactory"
    participant "PaymentStrategy"
    participant "Database"

    Client->>"PaymentCreateView (API)": POST /api/payments/create (request data)
    "PaymentCreateView (API)"->>"PaymentCreateView (API)": Check user permissions and status
    alt User is approved to sell
        "PaymentCreateView (API)"->>"PaymentCreateSerializer": Create and validate data
        "PaymentCreateSerializer"-->>"PaymentCreateView (API)": Return validated data
        "PaymentCreateView (API)"->>"to_payment_model()": Convert validated data to Payment object
        "to_payment_model()"->>"Database": Create/Update Customer, Address, Card, etc.
        "to_payment_model()"-->>"PaymentCreateView (API)": Return Payment instance
        alt Request type is "subscription"
            "PaymentCreateView (API)"->>"SubscriptionFactory": Get subscription strategy
            "SubscriptionFactory"-->>"PaymentCreateView (API)": Return SubscriptionStrategy
            "PaymentCreateView (API)"->>"SubscriptionStrategy": execute_subscription()
            "SubscriptionStrategy"-->>Client: Return subscription response
        else Regular Payment
            "PaymentCreateView (API)"->>"PaymentStrategyFactory": Get strategy for paymentMethod
            "PaymentStrategyFactory"-->>"PaymentCreateView (API)": Return correct PaymentStrategy
            "PaymentCreateView (API)"->>"PaymentStrategy": executePayment(payment_instance, card)
            activate "PaymentStrategy"
            "PaymentStrategy"->>"Database": Save Payment details
            "Database"-->>"PaymentStrategy": Return created_payment
            deactivate "PaymentStrategy"
            "PaymentStrategy"-->>"PaymentCreateView (API)": Return created_payment
            "PaymentCreateView (API)"->>"PaymentSerializer": Serialize created_payment
            "PaymentSerializer"-->>"PaymentCreateView (API)": Return serialized payment data
            "PaymentCreateView (API)"-->>Client: 201 CREATED (Success with payment data)
        end
    else User is not approved
        "PaymentCreateView (API)"-->>Client: 400 BAD REQUEST (Error: Seller not available)
    end

    alt Antifraud Error during executePayment
        "PaymentStrategy"-->>"PaymentCreateView (API)": raise AntifraudPaymentError
        "PaymentCreateView (API)"-->>Client: 400 BAD REQUEST (Error: Antifraud failure)
    end

    alt Validation Fails
         "PaymentCreateSerializer"-->>"PaymentCreateView (API)": Return validation errors
         "PaymentCreateView (API)"-->>Client: 400 BAD REQUEST (Error: Invalid data)
    end

```

---

## API Reference

#### Criar pagamento

```http
POST /api/checkout/
```

### Payload (Request Body)

```json
{
    "user": 1,
    "amount": 150.75,
    "paymentMethod": "credit_card",
    "installments": 1,
    "postbackUrl": "https://my-store.com/webhook/payment-update",
    "exId": "order-xyz-12345",
    "checkoutUrl": "https://my-store.com/checkout/fgh-678",
    "refererUrl": "https://my-store.com/product/awesome-product",
    "antifraud_profiling_attempt_reference": "antifraud-session-id-9876",
    "customer": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "docNumber": "12345678909",
        "docType": "CPF",
        "phone": "11987654321"
    },
    "address": {
        "street": "Elm Street",
        "number": "123",
        "complement": "Apt 4B",
        "zipCode": "01001000",
        "neighborhood": "Downtown",
        "city": "Sao Paulo",
        "state": "SP"
    },
    "card": {
        "holderName": "John Doe",
        "number": "****************",
        "cvv": "123",
        "expMonth": "12",
        "expYear": "2028"
    },
    "items": [
        {
            "name": "Awesome T-Shirt",
            "quantity": 1,
            "unitPrice": 100.50
        },
        {
            "name": "Cool Mug",
            "quantity": 1,
            "unitPrice": 50.25
        }
    ],
    "splits": [
        {
            "seller_id": 2,
            "percentage": 10
        }
    ]
}
```

# Descrição do Payload de Pagamento

Este documento descreve cada campo do objeto JSON enviado para o sistema de pagamento, incluindo suas relações e modelos de dados associados.

## PaymentCreateSerializer

---

| Campo          | Tipo     | Descrição                                  | Obrigatório |
|----------------|----------|--------------------------------------------|-------------|
| user           | String   | O ID do usuário vendedor que está criando o pagamento. | Sim |
| amount         | Decimal  | O valor total da transação. Deve ser maior que zero. | Sim |
| paymentMethod  | String   | O método de pagamento. Opções válidas são: credit_card, boleto, pix, picpay, nupay, googlepay, applepay, openfinance_nubank, threeDs. | Sim |
| customer       | Object   | Um objeto JSON contendo os detalhes do cliente (nome, email, docNumber, docType, telefone). | Sim |
| address        | Object   | Um objeto JSON contendo os detalhes do endereço de cobrança do cliente. | Sim |
| items          | Array    | Um array JSON de objetos, onde cada objeto representa um item no carrinho (nome, quantidade, preçoUnitário). | Sim |
| installments   | Integer  | O número de parcelas para o pagamento. Obrigatório para credit_card, googlepay, applepay e threeDs. | Não |
| card           | Object   | Um objeto JSON com detalhes do cartão de crédito. Obrigatório se paymentMethod for credit_card. | Não |
| postbackUrl    | String   | Uma URL para a qual as atualizações de status do pagamento serão enviadas. | Não |
| expiresInDays  | Integer  | O número de dias até o pagamento (por exemplo, um boleto) expirar. | Não |
| feeByItem      | Boolean  | Um booleano indicando se as taxas devem ser calculadas por item. O padrão é falso. | Não |
| affiliate      | Object   | Um objeto JSON contendo detalhes do afiliado (id, porcentagem). | Não |
| googlepay      | Object   | Um objeto JSON com dados do token do Google Pay. Obrigatório se paymentMethod for googlepay. | Não |
| applepay       | Object   | Um objeto JSON com dados do token do Apple Pay. Obrigatório se paymentMethod for applepay. | Não |
| openFinanceNubank | Object | Um objeto JSON com dados do Open Finance (Nubank). | Não |
| mercadopago    | Object   | Um objeto JSON contendo dados específicos do Mercado Pago. | Não |
| splits         | Array    | Um array JSON para divisões de comissão de co-produtor. Cada objeto precisa de um seller_id e porcentagem. | Não |
| exId           | String   | Um identificador externo para a transação, como um ID de pedido do sistema do vendedor. | Não |
| checkoutUrl    | String   | A URL da página de checkout onde o pagamento foi iniciado. | Não |
| refererUrl     | String   | A URL da qual o usuário foi encaminhado para o checkout. | Não |
| antifraud_profiling_attempt_reference | String | Um ID de referência de uma sessão de criação de perfil antifraude. | Não |
| external_authentication | Object | Um objeto JSON para dados de autenticação 3-D Secure (cavv, xid, etc.). | Não |



---

# Respostas da API

## Resposta de Sucesso (201 CREATED)

```json
{
    "status": "success",
    "data": {
        "id": "pay_12345abcde",
        "status": "paid",
        "amount": "100.50",
        "paymentMethod": "credit_card",
        "customer": {
            "name": "John Doe",
            "email": "<EMAIL>"
        },
        "createdAt": "2025-07-16T17:23:00Z"
    }
}
```

## Resposta de Erro (400 BAD REQUEST)

```json
{
    "status": "error",
    "message": "Esse método de pagamento não está disponível para esse vendedor."
}
```

---

# Lógica de Negócio Principal

1. **Autenticação e Permissões**: A view verifica se o usuário está autenticado e possui a permissão BlockMovingpayPermission.
2. **Verificação de Status do Usuário**: Verifica se a conta do usuário está em um status que permite vender (approved, pre_approved, etc.), com base nas configurações da aplicação.
3. **Serialização e Validação**: Os dados da requisição são validados usando PaymentCreateSerializer. Se a validação falhar, uma resposta 400 é retornada com os erros.
4. **Criação do Modelo de Pagamento**: Uma instância do modelo Payment é criada em memória a partir dos dados validados. Isso envolve criar ou atualizar objetos Customer e Card conforme necessário.
5. **Verificação do Método de Pagamento**: O método _is_payment_method_allowed_for_user verifica se o usuário atual tem permissão para usar o método de pagamento solicitado.
6. **Seleção de Estratégia**:
   - Se o tipo for subscription, o SubscriptionFactory é usado para obter a estratégia apropriada para criar um pagamento recorrente.
   - Caso contrário, o PaymentStrategyFactory é usado para obter a estratégia para o paymentMethod especificado.
7. **Execução do Pagamento**: O método executePayment (ou execute_subscription) da estratégia selecionada é chamado. Este método contém a lógica específica para interagir com o gateway ou serviço de pagamento.
8. **Tratamento de Erros**: Qualquer AntifraudPaymentError ou outras exceções durante a execução do pagamento são capturadas, e uma resposta de erro formatada é retornada ao cliente.
9. **Resposta de Sucesso**: Se o pagamento for bem-sucedido, o objeto Payment criado é serializado e retornado em uma resposta 201 CREATED.

---

# Modelos de Banco de Dados

Os seguintes modelos de banco de dados estão diretamente envolvidos neste processo:

- **Payment**: O modelo central que armazena todas as informações sobre uma transação de pagamento.
- **Customer**: Armazena informações do cliente, vinculado a um User.
- **Card**: Armazena informações tokenizadas de cartão de crédito, vinculado a um Customer.
- **Split**: Armazena informações sobre como um pagamento é dividido entre diferentes usuários (produtor, co-produtores, afiliados).
- **User**: O modelo de usuário padrão do Django, com campos adicionais para status e permissões relacionadas a pagamentos.
- **Settings**: Um modelo para configurações em toda a aplicação, como canSellWithoutApproval.

---