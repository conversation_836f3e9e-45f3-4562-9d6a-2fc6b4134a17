---
title: '<PERSON><PERSON><PERSON> pagamento via Pix Automático'
description: 'API para criar assinaturas recorrentes com cobrança automática via Pix, ideal para serviços ou produtos com faturamento periódico.'
---

# Introdução

Esta API permite criar uma **assinatura recorrente com cobrança automática via PIX**, ideal para serviços ou produtos com faturamento periódico.

Ao realizar a chamada, o sistema registra um cliente, seu endereço e os detalhes da recorrência (valor, frequência, número de ciclos, tentativas de cobrança e período de teste). A cada ciclo, é gerado uma **cobrança automaticamente descontada na conta do usuário** e pode ser enviado ao cliente para pagamento.

Essa solução dispensa o uso de cartão de crédito ou débito automático, oferecendo uma alternativa moderna, segura e eficiente para cobrança recorrente no Brasil.

# Funcionamento Interno da API

A seguir, um diagrama simplificado do fluxo de execução para criação do checkout com assinatura usando pix automático:

---

## API Reference

#### Criar checkout com pix automático

```http
POST /api/checkout/
```

### Payload (Request Body)

```json
{
  "amount": 126.00,
  "customer": {
    "name": "Carlos Eduardo Mendes",
    "email": "<EMAIL>",
    "phone": "11987654321",
    "docType": "cpf",
    "docNumber": "12345678901",
    "ip": "************",
    "fingerprint": "a1b2c3d4e5f67890",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  },
  "address": {
    "country": "BR",
    "state": "SP",
    "city": "São Paulo",
    "zipcode": "04567-002",
    "street": "Avenida Paulista",
    "complement": "Apto 1203",
    "number": "1578"
  },
  "items": [
    {
      "title": "Assinatura Premium Clube de Livros",
      "unitPrice": 42.00,
      "quantity": 3,
      "tangible": false,
      "externalRef": "SUBS-PREMIUM-2025",
      "subscription": {
        "recurrence_period": 1,
        "quantity_recurrences": 12,
        "trial_days": 7,
        "max_retries": 5,
        "amount": 42.00
      }
    }
  ],
  "paymentMethod": "pix_auto"
}
```
# Descrição do Payload de Pagamento

Este documento descreve cada campo do objeto JSON enviado para um sistema de pagamento, incluindo suas relações e modelos de dados associados.


## PaymentSerializer

| Campo          | Tipo     | Descrição                                  |
|----------------|----------|--------------------------------------------|
| amount         | number   | Valor total da compra.                      |
| customer       | object   | Dados do cliente (ver tabela `Customer`). |
| address        | object   | Endereço de entrega/faturamento (ver `Address`). |
| items          | array    | Lista de produtos/serviços (ver `Item`).  |
| paymentMethod  | string   | Método de pagamento (ex: "pix_auto").                  |

---

## Customer

| Campo       | Tipo    | Descrição                      |
|-------------|---------|-------------------------------|
| name        | string  | Nome completo do cliente.      |
| email       | string  | Email do cliente.              |
| phone       | string  | Telefone sem formatação.       |
| docType     | string  | Tipo de documento (ex: cpf).  |
| docNumber   | string  | Número do documento.           |
| ip          | string  | IP do cliente.                |
| fingerprint | string  | Identificador do dispositivo. |
| userAgent   | string  | Informações do navegador.      |

---

## Address

| Campo       | Tipo    | Descrição                        |
|-------------|---------|---------------------------------|
| country     | string  | Código do país (ex: "BR").      |
| state       | string  | Estado (ex: "SP").               |
| city        | string  | Cidade.                         |
| zipcode     | string  | Código postal (CEP).             |
| street      | string  | Nome da rua.                   |
| number      | string  | Número do imóvel.               |
| complement  | string  | Complemento (opcional).         |

---

## Item

| Campo          | Tipo     | Descrição                             |
|----------------|----------|-------------------------------------|
| title          | string   | Nome do produto/serviço.             |
| unitPrice      | number   | Preço unitário.                      |
| quantity       | integer  | Quantidade comprada.                 |
| tangible       | boolean  | Produto físico (true) ou digital (false). |
| externalRef    | string   | Referência externa do produto.       |
| subscription   | object?  | Dados da assinatura (opcional, ver tabela abaixo). |

---

## Subscription (Por item)

| Campo               | Tipo     | Descrição                             |
|---------------------|----------|-------------------------------------|
| recurrence_period    | integer  | Período da recorrência (meses).      |
| quantity_recurrences | integer  | Número de ciclos.                    |
| trial_days          | integer  | Dias de teste grátis.                 |
| max_retries         | integer  | Tentativas máximas em falhas.         |
| amount              | number   | Valor cobrado por ciclo.              |

---

# Relações entre modelos

- `PaymentRequest` agrega `Customer`, `Address` e lista de `Item`s.
- Cada `Item` pode conter uma `Subscription` para assinaturas.
