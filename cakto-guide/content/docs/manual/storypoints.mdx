---
title: '<PERSON><PERSON><PERSON> para Marcar Story Points'
description: 'Sistema para estimar o esforço das tarefas baseado na duração prevista, facilitando o planejamento do time.'
---

## 1. O que são story points?

Story points são uma unidade de medida que representa o **esforço relativo necessário** para completar uma tarefa ou história.
Eles não são horas, mas uma forma de comparar tarefas entre si.

---

## 2. Por que usar story points?

- Medir esforço relativo
- Ajudar na previsão e planejamento
- Facilitar o alinhamento do time
- Identificar complexidade, riscos e trabalho envolvido

---

## 3. <PERSON><PERSON><PERSON> de Story Points e suas regras

Utilizamos a sequência Fibonacci:
`1, 2, 3, 5, 8, 13...`

**Regras definidas para pontuação e esforço estimado:**

| Pontos | Esforço estimado            |
|--------|----------------------------|
| 1      | Menos de 1 dia             |
| 2      | 1 dia e meio               |
| 3      | 2 a 3 dias                 |
| 5      | 5 dias                     |
| 8      | 7 a 10 dias                |
| >8     | Tarefas muito grandes **precisam ser quebradas em módulos menores** |

---

## 4. Quem deve participar da estimativa?

- Todo o time de desenvolvimento
- Product Owner (para esclarecer dúvidas)
- Tech Lead (para facilitar a discussão e garantir alinhamento)

---

## 5. Como marcar story points — Passo a Passo

### Passo 1: Entenda a tarefa
- Leia a descrição completa.
- Tire dúvidas com o Product Owner.

### Passo 2: Considere os fatores
- **Complexidade técnica**
- **Quantidade de trabalho**
- **Riscos e incertezas**

### Passo 3: Compare com uma tarefa base
- Exemplo: “A tarefa X é 3 pontos, porque é um pouco mais complexa que a tarefa base de 2 pontos.”

### Passo 4: Faça a votação (Planning Poker)
- Cada membro do time escolhe um valor da escala que acha adequado para a tarefa.
- Todos revelam ao mesmo tempo.
- Se houver divergência, discutem as razões.
- Repitam até chegarem a um consenso.

---

## 6. Regras importantes

- Story points são relativos ao time.
- Não converta diretamente para horas, mas utilize a tabela acima para referência de esforço.
- Quebre tarefas muito grandes (mais que 8 pontos) em partes menores.
- Não altere a estimativa depois que a sprint começa.
- Use story points para ajudar o planejamento, não para avaliar desempenho individual.

---

## 7. Dicas para o time

- Seja honesto e realista.
- Não estimule competição entre membros.
- Foque no esforço, não na velocidade.
- Sempre revise e aprimore o processo com retrospectivas.

---

## 8. Exemplo prático

| Tarefa                    | Pontos | Justificativa                         |
|--------------------------|--------|-------------------------------------|
| Criar formulário simples | 2      | Pouca complexidade e pouco trabalho |
| Integrar com API externa  | 5      | Complexidade média e dependências   |
| Refatorar módulo crítico  | 8      | Alta complexidade e risco elevado   |

---

*Documentação criada para facilitar o alinhamento e eficiência na estimativa do time.*
