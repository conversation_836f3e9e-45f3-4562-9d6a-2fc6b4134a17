---
title: 'Destacar relação entre usuários e recompensas'
description: 'API para relacionar os usuários e suas recompensas por faturamento de forma mais evidente, dividido por categorias.'
---

# Introdução

Esta API tem o intuito de integrar a área de frontend com o backend do sistema para obter informações sobre os ranks disponíveis e seus critérios de classificação.

Ao obter os dados referentes ao usuário logado através do endpoint `/api/user` será retornada a informação de `target_rank` com o código do rank alvo do usuário. Utilizando o endpoint `/api/ranks` será retornada uma lista de ranks ativos, contendo todas as informações sobre eles. Assim, o desenvolvedor consegue obter mais informações sobre o rank alvo do usuário logado.

Essa lógica permite uma separação clara de categorias e classificações, atribuindo recompensas à essas categorias e destacando a conquista do usuário.


## API Reference
---

## Alterações endpoint de User
#### Obter informações do usuário

```http
GET /api/user/
```

### Response Body

```json
{
	'id': int,
	'email': LowercaseEmailField,
	'first_name': CharField,
	'last_name': CharField,
	'picture': ImageField,
	'cpf': CharField,
	'cnpj': CharField,
	'phoneCountryCode': CharField,
	'cellphone': CharField,
	'address': CharField,
	'country': CharField,
	'state': CharField,
	'viewPageAs': CharField['student', 'producer'],
	'cep': CharField,
	'city': CharField,
	'totalSales': Decimal,
	'nextAward': Award | None,
  'target_rank': str,
	'companies': list[dict['id': int, 'name': str]],
	'companyStatus': str,
	'threeDsEnabled': Boolean,
	'refundRequest': Boolean,
	'is_staff': Boolean,
	'is_customer: Boolean,
	'is_producer': Boolean,
	'has_usable_password': Boolean,
	'experimental_features': ManyToManyField,
	'emailValidated': Boolean,
	'emailValidatedAt': DateTimeField,
	'whatsappValidated': Boolean,
	'whatsappValidatedAt': DateTimeField,
	'next_nps_survey_date': DateTimeField,
	'app_reviewed_at': DateTimeField
}
```

#### Alterações da antiga versão

Foi adicionado o campo de `target_rank` ao response do endpoint, contendo o código do rank alvo. Em conjunto à essa adição, foi desenvolvido o mecanismo de cache para essa informação e adaptada a função de nextAward para obter primeiramente através do próprio rank e segundamente pela forma original, evitando quebrar o funcionamento do sistema antes da implementação correta.

### **Descrição do Response de User**

Este documento descreve cada campo do objeto JSON obtido na requisição.


### **UserSerializer**

| Campo          | Tipo     | Descrição                                  |
|----------------|----------|--------------------------------------------|
| id             | integer  | Identificação do usuário                   |
| email          | string   | Email do usuário                           |
| first_name     | string   | Primeiro nome do usuário                   |
| last_name      | string   | Sobrenome do usuário                       |
| picture        | Image    | Foto do usuário                            |
| cpf            | string   | CPF do usuário                             |
| cnpj           | string   | CNPJ do usuário                            |
| phoneCountryCode | string | Código de área do telefone do usuário      |
| cellphone      | string   | Telefone do usuário                        |
| address        | string   | Endereço do usuário                        |
| country        | string   | País do usuário                            |
| state          | string   | Estado do usuário                          |
| viewPageAs     | string   | Tipo de visualização: Aluno ou Produtor    |
| cep            | string   | CEP do usuário                             |
| city           | string   | Cidade do usuário                          |
| totalSales     | decimal  | Total faturado pelo usuário                |
| nextAward      | object?  | Próxima recompensa a ser obtida            |
| target_rank    | string   | Próximo Rank do usuário                    |
| companies      | list     | Empresas relacionadas ao usuário           |
| companyStatus  | string   | Status da empresa do usuário               |
| threeDsEnabled | boolean  | Confirmação se o 3DS do usuário está habilitado |
| refundRequest  | boolean  | Confirmação se é uma requisição de reembolso |
| is_staff       | boolean  | Confirmação se o usuário é um funcionário  |
| is_customer    | boolean  | Confirmação se o usuário é um cliente      |
| is_producer    | boolean  | Confirmação se o usuário é um produtor     |
| has_usable_password | boolean | Confirmação se a senha é utilizável    |
| experimental_features | ManyToMany | Funcionalidades em teste que o usuário tem acesso |
| emailValidated | boolean  | Confirmação se o email está validado       |
| emailValidatedAt | datetime | Data de validação do email               |
| whatsappValidated | boolean | Confirmação se o WhatsApp está validado  |
| whatsappValidatedAt | datetime | Data de validação do WhatsApp         |
| next_nps_survey_date | datetime | Data da próxima pesquisa NPS         |
| app_reviewed_at | datetime | Última revisão do app                     |

---

## Endpoint de Rank
#### Obter lista de ranks ativos

```http
GET /api/ranks/
```

### Response Body

```json
{
results: [{
	"code": str,
	"label": str,
	"min_value": Decimal,
	"max_value": Decimal | None,
	"status": str,
	"createdAt": Datetime,
	"updatedAt": Datetime
}, ...]
}
```

#### Implementação inicial

Cada recompensa deve ser relacionada a um objeto Rank do model, de acordo com os limites de faturamento total do usuário, permitindo a identificação das recompensas através do Rank e que futuramente, caso necessário, um Rank possua mais de uma recompensa ou que a recompensa para esse Rank seja alterada facilmente.

Foi implementado também um sistema de cache para otimizar a performance para retornar os dados correspondentes aos Ranks evitando consultas excessivas ao banco de dados.

### **Descrição do Response de Ranks**

Este documento descreve cada campo do objeto JSON obtido na requisição.

### **RankSerializer**

| Campo          | Tipo     | Descrição                                  |
|----------------|----------|--------------------------------------------|
| code           | string   | Código do Rank                             |
| label          | string   | Título do Rank                             |
| min_value      | decimal  | Valor mínimo de faturamento para se enquadrar ao Rank |
| max_value      | decimal  | Valor máximo de faturamento para se enquadrar ao Rank |
| status         | string   | Status de disponibilidade do Rank          |
| createdAt      | datetime | Data de criação do Rank                    |
| updatedAt      | datetime | Data da última atualização do Rank         |

---
