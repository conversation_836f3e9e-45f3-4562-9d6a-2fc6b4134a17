@import "tailwindcss";
@import "@repo/tailwind-config/theme.css";
@import "@repo/tailwind-config/tailwind-animate.css";

@variant dark (&:where(.dark, .dark *));

/* Theme-aware background that respects light/dark mode */
body {
    background: var(--background);
    min-height: 100vh;
}

/* Ensure html also has the background for seamless transition */
html {
    background: var(--background);
    min-height: 100vh;
}

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply bg-card! top-[4.5rem] md:h-[calc(100dvh-4.5rem)]!;

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply no-underline!;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply bg-muted!;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}
