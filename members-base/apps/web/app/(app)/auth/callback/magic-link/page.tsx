"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent } from "@ui/components/card";
import { LoaderIcon, AlertCircleIcon } from "lucide-react";
import { useSession } from "@saas/auth/hooks/use-session";

export default function MagicLinkCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);
  const { user, loaded: sessionLoaded } = useSession();

  useEffect(() => {
    const processMagicLink = async () => {
      try {
        setIsProcessing(true);

        const token = searchParams.get("token");
        const callbackURL = searchParams.get("callbackURL");

        if (!token) {
          setError("Token de autenticação não encontrado");
          return;
        }

        if (!sessionLoaded) {
          return;
        }

        if (user) {
          const redirectPath = callbackURL || "/app";
          router.replace(redirectPath);
          return;
        }

        await new Promise(resolve => setTimeout(resolve, 3000));

        if (!user) {
          setError("Link de autenticação inválido ou expirado");
          return;
        }

      } catch (err) {
        console.error("Erro ao processar magic link:", err);
        setError("Erro ao processar autenticação. Por favor, tente novamente.");
      } finally {
        setIsProcessing(false);
      }
    };

    processMagicLink();
  }, [searchParams, router, sessionLoaded, user]);

  if (isProcessing || !sessionLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <LoaderIcon className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <h2 className="text-lg font-semibold mb-2">Verificando autenticação</h2>
              <p className="text-muted-foreground">
                Processando seu link de acesso...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <AlertCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-lg font-semibold mb-2">Erro de Autenticação</h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={() => router.push("/auth/login")}
                className="text-primary hover:underline"
              >
                Voltar para a página de login
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}
