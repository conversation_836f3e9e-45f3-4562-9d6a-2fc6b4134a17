"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent } from "@ui/components/card";
import { LoaderIcon } from "lucide-react";

export default function DjangoSSOCallback() {
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
        const error = searchParams.get("error");
        const errorDescription = searchParams.get("error_description");

        if (error) {
            console.error("SSO Error:", error, errorDescription);
            router.push("/auth/login?error=sso_failed");
            return;
        }

        const timer = setTimeout(() => {
            router.push("/app");
        }, 2000);

        return () => clearTimeout(timer);
    }, [router, searchParams]);

    return (
        <div className="flex items-center justify-center min-h-screen bg-background">
            <Card className="w-full max-w-md">
                <CardContent className="flex flex-col items-center justify-center p-6">
                    <div className="text-center">
                        <LoaderIcon className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                        <h2 className="text-lg font-semibold mb-2">Processando autenticação</h2>
                        <p className="text-muted-foreground">
                            Aguarde enquanto validamos suas credenciais...
                        </p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
