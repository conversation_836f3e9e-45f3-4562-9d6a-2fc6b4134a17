"use client";

import { useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";


import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	FormDescription,
} from "@ui/components/form";
import { InfoIcon } from "lucide-react";
import type { CourseFormData } from "../types";
import { ImageUpload } from "./ImageUpload";
import { useOrganizations } from "../hooks/useOrganizations";
import { Card, CardHeader, CardTitle, CardContent } from "@ui/components/card";

const courseSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório").max(100, "Nome deve ter no máximo 100 caracteres"),
	description: z.string().optional(),
	organizationId: z.string().min(1, "Organização é obrigatória"),
	community: z.string().optional(),
	link: z.string().url("URL deve ser válida").optional().or(z.literal("")),
	logo: z.string().url("URL da logo deve ser válida").optional().or(z.literal("")),
});

type CourseFormFieldsData = z.infer<typeof courseSchema>;

interface CourseBasicFormProps {
	data: CourseFormData;
	onUpdate: (data: Partial<CourseFormData>) => void;
	onNext: () => void;
}

export function CourseBasicForm({ data, onUpdate, onNext }: CourseBasicFormProps) {
	const { organizations, isLoading: isLoadingOrganizations } = useOrganizations();
	const form = useForm<CourseFormFieldsData>({
		resolver: zodResolver(courseSchema),
		defaultValues: {
			name: data.name || "",
			description: data.description || "",
			organizationId: data.organizationId || "",
			community: data.community || "",
			link: data.link || "",
			logo: data.logo || "",
		},
	});

	const onSubmit = useCallback((formData: CourseFormFieldsData) => {
		onUpdate(formData);
		onNext();
	}, [onUpdate, onNext]);

	const handleFieldChange = useCallback((field: keyof CourseFormFieldsData, value: any) => {
		onUpdate({ [field]: value });
	}, [onUpdate]);

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<InfoIcon className="h-5 w-5 text-primary" />
						Informações Básicas
					</CardTitle>
					<p className="text-muted-foreground">
						Configure as informações principais do seu curso
					</p>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Nome do Curso *</FormLabel>
										<FormControl>
											<Input
												placeholder="Ex: React Avançado"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													handleFieldChange("name", e.target.value);
												}}
											/>
										</FormControl>
										<FormDescription>
											Este será o nome principal exibido no curso
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="description"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Descrição</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Descreva brevemente o que os alunos aprenderão neste curso..."
												className="min-h-[100px]"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													handleFieldChange("description", e.target.value);
												}}
											/>
										</FormControl>
										<FormDescription>
											Uma descrição atrativa ajuda os alunos a entenderem o conteúdo
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="organizationId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Organização *</FormLabel>
										<Select
											value={field.value}
											onValueChange={(value) => {
												field.onChange(value);
												handleFieldChange("organizationId", value);
											}}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Selecione uma organização" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{organizations.map((org) => (
													<SelectItem key={org.id} value={org.id}>
														{org.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormDescription>
											A organização à qual este curso pertencerá
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="community"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Comunidade</FormLabel>
										<FormControl>
											<Input
												placeholder="Nome da comunidade relacionada"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													handleFieldChange("community", e.target.value);
												}}
											/>
										</FormControl>
										<FormDescription>
											Opcional: Nome da comunidade ou grupo relacionado ao curso
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="link"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Link do Curso</FormLabel>
										<FormControl>
											<Input
												placeholder="https://exemplo.com/curso"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													handleFieldChange("link", e.target.value);
												}}
											/>
										</FormControl>
										<FormDescription>
											Opcional: Link externo relacionado ao curso
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="logo"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Logo do Curso</FormLabel>
										<FormControl>
											<div className="space-y-4">
												<ImageUpload
													value={field.value}
													onChange={(url) => {
														field.onChange(url);
														handleFieldChange("logo", url);
													}}
													organizationId={data.organizationId}
													fileType="course-logo"
													// label="Logo do Curso"
													description="Selecione uma imagem para o logo do curso"
												/>

											</div>
										</FormControl>

										<FormMessage />
									</FormItem>
								)}
							/>

							{/* <div className="flex justify-end pt-4">
								<Button
									type="submit"
									disabled={!form.formState.isValid}
								>
									Continuar
								</Button>
							</div> */}
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
