"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { TrendingDownIcon, TrendingUpIcon } from "lucide-react";

interface StatsTileProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  currency?: string;
  percentage?: boolean;
}

export function StatsTile({
  title,
  value,
  change,
  changeLabel,
  currency,
  percentage,
}: StatsTileProps) {
  const formatValue = () => {
    if (currency) {
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency,
      }).format(Number(value));
    }

    if (percentage) {
      return `${value}%`;
    }

    return value;
  };

  const formatChange = () => {
    if (change === undefined) return null;

    const isPositive = change > 0;
    const Icon = isPositive ? TrendingUpIcon : TrendingDownIcon;
    const color = isPositive ? "text-green-600" : "text-red-600";

    return (
      <div className={`flex items-center gap-1 text-sm ${color}`}>
        <Icon className="h-4 w-4" />
        <span>{Math.abs(change)}%</span>
        {changeLabel && <span className="text-muted-foreground">{changeLabel}</span>}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue()}</div>
        {formatChange()}
      </CardContent>
    </Card>
  );
}
