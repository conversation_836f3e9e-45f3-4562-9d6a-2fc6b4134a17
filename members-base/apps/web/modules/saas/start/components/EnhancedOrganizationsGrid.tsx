"use client";
import { config } from "@repo/config";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useOrganizationStats } from "@saas/organizations/hooks/useOrganizationStats";
import { Card } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	PlusCircleIcon,
	Users,
	BookOpen,
	EyeIcon,
	TrendingUpIcon,
	ActivityIcon,
	CalendarIcon,
	ArrowRightIcon,
	LoaderIcon,
	AlertCircleIcon
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";

interface WorkspaceCardProps {
	organization: {
		id: string;
		name: string;
		slug: string;
		logo?: string | null;
		createdAt: string;
		stats: {
			totalMembers: number;
			activeMembers: number;
			totalCourses: number;
			totalEnrollments: number;
			totalVitrines: number;
			publishedVitrines: number;
			totalViews: number;
			daysSinceLastActivity: number | null;
		};
	};
	onSelect: (slug: string) => void;
}

function WorkspaceCard({ organization, onSelect }: WorkspaceCardProps) {
	const getStatusBadge = () => {
		if (organization.stats.totalCourses === 0 && organization.stats.totalVitrines === 0) {
			return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Novo</Badge>;
		}
		if (organization.stats.daysSinceLastActivity && organization.stats.daysSinceLastActivity > 30) {
			return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Inativo</Badge>;
		}
		return <Badge className="bg-green-100 text-green-800 border-green-200">Ativo</Badge>;
	};

	const getLastActivityText = () => {
		if (!organization.stats.daysSinceLastActivity) {
			return "Sem atividade recente";
		}
		if (organization.stats.daysSinceLastActivity === 0) {
			return "Atividade hoje";
		}
		if (organization.stats.daysSinceLastActivity === 1) {
			return "Atividade ontem";
		}
		return `Atividade há ${organization.stats.daysSinceLastActivity} dias`;
	};

	const getEngagementScore = () => {
		if (organization.stats.totalMembers === 0) return 0;
		return Math.round((organization.stats.activeMembers / organization.stats.totalMembers) * 100);
	};

	return (
		<Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border hover:border-primary/30 overflow-hidden">
			<div className="p-6">
				{/* Header */}
				<div className="flex items-start justify-between mb-4">
					<div className="flex items-center gap-3">
						<OrganizationLogo
							name={organization.name}
							logoUrl={organization.logo}
							className="size-12"
						/>
						<div className="min-w-0 flex-1">
							<h3 className="font-semibold text-lg text-foreground truncate">
								{organization.name}
							</h3>
							<div className="flex items-center gap-2 mt-1">
								{getStatusBadge()}
								<span className="text-xs text-muted-foreground">
									Criado em {new Date(organization.createdAt).toLocaleDateString('pt-BR')}
								</span>
							</div>
						</div>
					</div>
				</div>

				{/* Key Metrics */}
				<div className="grid grid-cols-2 gap-4 mb-4">
					<div className="text-center p-3 bg-primary/5 rounded-lg">
						<div className="flex items-center justify-center gap-2 mb-1">
							<Users className="h-4 w-4 text-primary" />
							<span className="text-sm font-medium text-muted-foreground">Membros</span>
						</div>
						<div className="text-xl font-bold text-foreground">
							{organization.stats.totalMembers}
						</div>
						{organization.stats.totalMembers > 0 && (
							<div className="text-xs text-muted-foreground">
								{organization.stats.activeMembers} ativos
							</div>
						)}
					</div>

					<div className="text-center p-3 bg-primary/5 rounded-lg">
						<div className="flex items-center justify-center gap-2 mb-1">
							<BookOpen className="h-4 w-4 text-primary" />
							<span className="text-sm font-medium text-muted-foreground">Conteúdo</span>
						</div>
						<div className="text-xl font-bold text-foreground">
							{organization.stats.totalCourses + organization.stats.publishedVitrines}
						</div>
						<div className="text-xs text-muted-foreground">
							{organization.stats.totalCourses} cursos • {organization.stats.publishedVitrines} vitrines
						</div>
					</div>
				</div>

				{/* Engagement & Activity */}
				<div className="space-y-3 mb-6">
					{/* Engagement Score */}
					{organization.stats.totalMembers > 0 && (
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm text-muted-foreground">Engajamento</span>
							</div>
							<div className="flex items-center gap-2">
								<div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
									<div
										className="h-full bg-primary transition-all duration-300"
										style={{ width: `${getEngagementScore()}%` }}
									/>
								</div>
								<span className="text-sm font-medium">{getEngagementScore()}%</span>
							</div>
						</div>
					)}

					{/* Last Activity */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<ActivityIcon className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm text-muted-foreground">Última atividade</span>
						</div>
						<span className="text-sm font-medium">
							{getLastActivityText()}
						</span>
					</div>

					{/* Views */}
					{organization.stats.totalViews > 0 && (
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<EyeIcon className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm text-muted-foreground">Visualizações</span>
							</div>
							<span className="text-sm font-medium">
								{organization.stats.totalViews.toLocaleString('pt-BR')}
							</span>
						</div>
					)}
				</div>

				{/* Action Button */}
				<Button
					onClick={() => onSelect(organization.slug)}
					className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
					variant="outline"
				>
					<span>Acessar Workspace</span>
					<ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
				</Button>
			</div>
		</Card>
	);
}

export function EnhancedOrganizationsGrid() {
	const t = useTranslations();
	const { setActiveOrganization } = useActiveOrganization();
	const { data: organizations, isLoading, error } = useOrganizationStats();

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h2 className="text-2xl font-semibold">
							{t("organizations.organizationsGrid.title")}
						</h2>
						<p className="text-muted-foreground">
							Gerencie e acesse seus workspaces
						</p>
					</div>
					{config.organizations.enableUsersToCreateOrganizations && (
						<Button asChild>
							<Link href="/app/new-organization">
								<PlusCircleIcon className="mr-2 h-4 w-4" />
								Novo Workspace
							</Link>
						</Button>
					)}
				</div>

				<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
					{Array.from({ length: 3 }).map((_, index) => (
						<Card key={index} className="p-6">
							<div className="flex items-center justify-center h-32">
								<LoaderIcon className="h-8 w-8 animate-spin text-muted-foreground" />
							</div>
						</Card>
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h2 className="text-2xl font-semibold">
							{t("organizations.organizationsGrid.title")}
						</h2>
						<p className="text-muted-foreground">
							Gerencie e acesse seus workspaces
						</p>
					</div>
					{config.organizations.enableUsersToCreateOrganizations && (
						<Button asChild>
							<Link href="/app/new-organization">
								<PlusCircleIcon className="mr-2 h-4 w-4" />
								Novo Workspace
							</Link>
						</Button>
					)}
				</div>

				<Card className="p-12">
					<div className="flex flex-col items-center justify-center text-center">
						<AlertCircleIcon className="h-12 w-12 text-red-500 mb-4" />
						<h3 className="text-lg font-semibold mb-2">Erro ao carregar workspaces</h3>
						<p className="text-muted-foreground mb-4">
							{error instanceof Error ? error.message : "Erro interno do servidor"}
						</p>
						<Button onClick={() => window.location.reload()} variant="outline">
							Tentar novamente
						</Button>
					</div>
				</Card>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-semibold">
						{t("organizations.organizationsGrid.title")}
					</h2>
					<p className="text-muted-foreground">
						Gerencie e acesse seus workspaces
					</p>
				</div>
				{config.organizations.enableUsersToCreateOrganizations && (
					<Button asChild>
						<Link href="/app/new-organization">
							<PlusCircleIcon className="mr-2 h-4 w-4" />
							Novo Workspace
						</Link>
					</Button>
				)}
			</div>

			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				{organizations?.filter(org => org.slug).map((organization) => (
					<WorkspaceCard
						key={organization.id}
						organization={{
							...organization,
							slug: organization.slug || "",
						}}
						onSelect={setActiveOrganization}
					/>
				))}

				{config.organizations.enableUsersToCreateOrganizations && (
					<Card className="group hover:shadow-lg transition-all duration-300 border-dashed border-2 border-muted-foreground/30 hover:border-primary/50 overflow-hidden">
						<Link
							href="/app/new-organization"
							className="flex flex-col items-center justify-center h-full min-h-[300px] text-center space-y-4 text-muted-foreground hover:text-primary transition-colors p-6"
						>
							<div className="p-4 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
								<PlusCircleIcon className="h-8 w-8" />
							</div>
							<div>
								<h3 className="font-semibold text-lg">
									{t("organizations.organizationsGrid.createNewOrganization")}
								</h3>
								<p className="text-sm mt-2 text-muted-foreground">
									Crie uma nova área de membros para compartilhar conhecimento
								</p>
							</div>
						</Link>
					</Card>
				)}
			</div>
		</div>
	);
}
