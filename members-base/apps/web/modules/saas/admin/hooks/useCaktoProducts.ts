import { useAdminCaktoProductsQuery } from '../lib/api'

interface CaktoProduct {
  id: string
  name: string
  description: string
  image?: string
  price: number
  type: 'course' | 'ebook' | 'mentorship'
  status: 'active' | 'inactive'
  organizationId: string
  contentDeliveries: string[]
  alreadyLinked: boolean
}

interface UseCaktoProductsOptions {
  organizationId?: string
  type?: string
  enabled?: boolean
}

export function useCaktoProducts(options: UseCaktoProductsOptions = {}) {
  const query = useAdminCaktoProductsQuery(options.enabled ?? true)

  return {
    products: query.data || [],
    isLoading: query.isLoading,
    error: query.error?.message || null,
    refetch: query.refetch
  }
}
