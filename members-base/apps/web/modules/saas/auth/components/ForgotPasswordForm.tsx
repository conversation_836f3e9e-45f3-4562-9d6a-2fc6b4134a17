"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { AlertTriangleIcon, ArrowLeftIcon, MailboxIcon } from "lucide-react";

import { authClient } from "@repo/auth/client";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Logo } from "@shared/components/Logo";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useForm } from "react-hook-form";
import * as z from "zod";

const formSchema = z.object({
	email: z.string().email(),
});

type FormValues = z.infer<typeof formSchema>;

export function ForgotPasswordForm() {
	const t = useTranslations();
	const { getAuthErrorMessage } = useAuthErrorMessages();

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
		},
	});

	const onSubmit = form.handleSubmit(async ({ email }) => {
		try {
			const redirectTo = new URL(
				"/auth/reset-password",
				window.location.origin,
			).toString();

			const { error } = await authClient.forgetPassword({
				email,
				redirectTo,
			});

			if (error) {
				throw error;
			}
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		}
	});

	return (
		<div className="grid min-h-svh lg:grid-cols-2">
			{/* Left Column - Banner */}
			<div className="relative hidden lg:block overflow-hidden">
				<img
					src="/images/banner1.jpg"
					alt="Cakto Members"
					className="absolute inset-0 h-full w-full object-cover"
				/>
				<div className="absolute inset-0 bg-black/40" />
				<div className="absolute inset-0 opacity-20 bg-gradient-to-br from-primary/20 to-primary/40" />
			</div>

			{/* Right Column - Form */}
			<div className="flex flex-col gap-4 p-6 md:p-10">
				{/* Logo Section */}
				<div className="flex justify-center gap-2 md:justify-start">
					<Link href="/">
						<Logo withLabel={true} />
					</Link>
				</div>

				{/* Form Section */}
				<div className="flex flex-1 items-center justify-center">
					<div className="w-full max-w-sm">
						<div className="grid gap-6">
							<div className="grid gap-2 text-left">
								<h1 className="text-3xl font-bold">
									{t("auth.forgotPassword.title")}
								</h1>
								<p className="text-muted-foreground">
									{t("auth.forgotPassword.message")}
								</p>
							</div>

							{form.formState.isSubmitSuccessful ? (
								<Alert variant="success">
									<MailboxIcon />
									<AlertTitle>
										{t("auth.forgotPassword.hints.linkSent.title")}
									</AlertTitle>
									<AlertDescription>
										{t("auth.forgotPassword.hints.linkSent.message")}
									</AlertDescription>
								</Alert>
							) : (
								<div className="grid gap-4">
									{form.formState.errors.root && (
										<Alert variant="error">
											<AlertTriangleIcon />
											<AlertTitle>
												{form.formState.errors.root.message}
											</AlertTitle>
										</Alert>
									)}

									<Form {...form}>
										<form
											className="space-y-4"
											onSubmit={onSubmit}
										>
											<FormField
												control={form.control}
												name="email"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															{t("auth.forgotPassword.email")}
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																autoComplete="email"
																placeholder="Digite seu e-mail"
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<Button
												className="w-full"
												loading={form.formState.isSubmitting}
											>
												{t("auth.forgotPassword.submit")}
											</Button>
										</form>
									</Form>

									<div className="mt-6 text-center text-sm">
										<Link href="/auth/login" className="text-foreground/60 hover:text-foreground">
											<ArrowLeftIcon className="mr-1 inline size-4 align-middle" />
											{t("auth.forgotPassword.backToSignin")}
										</Link>
									</div>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
