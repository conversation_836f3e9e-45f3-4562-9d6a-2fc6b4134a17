"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import { sessionQuery<PERSON>ey } from "@saas/auth/lib/api";
import { OrganizationInvitationAlert } from "@saas/organizations/components/OrganizationInvitationAlert";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Logo } from "@shared/components/Logo";
import {
	AlertTriangleIcon,
	ArrowRightIcon,
	MailboxIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { withQuery } from "ufo";
import { z } from "zod";
import { useSession } from "../hooks/use-session";
import { CaktoSSOButton } from "./CaktoSSOButton";

const formSchema = z.object({
	email: z.string().email(),
});

type FormValues = z.infer<typeof formSchema>;

export function CaktoLoginForm() {
	const t = useTranslations();
	const { getAuthErrorMessage } = useAuthErrorMessages();
	const router = useRouter();
	const queryClient = useQueryClient();
	const searchParams = useSearchParams();
	const { user, loaded: sessionLoaded } = useSession();

	const invitationId = searchParams.get("invitationId");
	const email = searchParams.get("email");
	const redirectTo = searchParams.get("redirectTo");

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: email ?? "",
		},
	});

	const redirectPath = invitationId
		? `/app/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	useEffect(() => {
		if (sessionLoaded && user) {
			router.replace(redirectPath);
		}
	}, [user, sessionLoaded]);

	const onSubmit: SubmitHandler<FormValues> = async (values) => {
		try {
			const { error } = await authClient.signIn.magicLink({
				...values,
				callbackURL: redirectPath,
			});

			if (error) {
				throw error;
			}
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		}
	};

	return (
		<div className="grid min-h-svh lg:grid-cols-2">
			{/* Left Column - Banner */}
			<div className="relative hidden lg:block overflow-hidden">
				<img
					src="/images/banner1.jpg"
					alt="Cakto Members"
					className="absolute inset-0 h-full w-full object-cover"
				/>
				<div className="absolute inset-0 bg-black/40" />
				<div className="absolute inset-0 opacity-20 bg-gradient-to-br from-primary/20 to-primary/40" />
			</div>

			{/* Right Column - Form */}
			<div className="flex flex-col gap-4 p-6 md:p-10">
				{/* Logo Section */}
				<div className="flex justify-center gap-2 md:justify-start">
					<Link href="/">
						<Logo withLabel={true} />
					</Link>
				</div>

				{/* Form Section */}
				<div className="flex flex-1 items-center justify-center">
					<div className="w-full max-w-sm">
						<div className="grid gap-6">
							<div className="grid gap-2 text-left">
								<h1 className="text-3xl font-bold">
									{t("auth.login.title")}
								</h1>
								<p className="text-muted-foreground">
									{t("auth.login.subtitle")}
								</p>
							</div>

							{form.formState.isSubmitSuccessful ? (
								<Alert variant="success">
									<MailboxIcon />
									<AlertTitle>
										{t("auth.login.hints.linkSent.title")}
									</AlertTitle>
									<AlertDescription>
										{t("auth.login.hints.linkSent.message")}
									</AlertDescription>
								</Alert>
							) : (
								<div className="grid gap-4">
									{invitationId && (
										<OrganizationInvitationAlert className="mb-6" />
									)}

									<CaktoSSOButton className="w-full" />

									{/* Divider */}
									<div className="relative">
										<div className="absolute inset-0 flex items-center">
											<span className="w-full border-t" />
										</div>
										<div className="relative flex justify-center text-xs uppercase">
											<span className="bg-background px-2 text-muted-foreground">
												{t("auth.login.continueWith")}
											</span>
										</div>
									</div>

									{form.formState.isSubmitted &&
										form.formState.errors.root?.message && (
											<Alert variant="error">
												<AlertTriangleIcon />
												<AlertTitle>
													{form.formState.errors.root.message}
												</AlertTitle>
											</Alert>
										)}

									<Form {...form}>
										<form
											className="space-y-4"
											onSubmit={form.handleSubmit(onSubmit)}
										>
											<FormField
												control={form.control}
												name="email"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															{t("auth.signup.email")}
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																autoComplete="email"
																placeholder="Digite seu e-mail"
															/>
														</FormControl>
													</FormItem>
												)}
											/>

											<Button
												className="w-full"
												type="submit"
												variant="secondary"
												loading={form.formState.isSubmitting}
											>
												{t("auth.login.sendMagicLink")}
											</Button>
										</form>
									</Form>

									{config.auth.enableSignup && (
										<div className="mt-6 text-center text-sm">
											<span className="text-foreground/60">
												{t("auth.login.dontHaveAnAccount")}{" "}
											</span>
											<Link
												href={withQuery(
													"/auth/signup",
													Object.fromEntries(searchParams.entries()),
												)}
											>
												{t("auth.login.createAnAccount")}
												<ArrowRightIcon className="ml-1 inline size-4 align-middle" />
											</Link>
										</div>
									)}
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
