'use client'

import { useState, useEffect } from 'react'
import { VideoLibraryProps } from '../../types'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { But<PERSON> } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Badge } from '@/modules/ui/components/badge'
import { ScrollArea } from '@/modules/ui/components/scroll-area'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/modules/ui/components/dialog'
import {
  Search,
  Video,
  Upload,
  Play,
  Clock,
  Calendar,
  X,
  Minimize2,
  Maximize2,
  Filter,
  Grid3X3,
  List
} from 'lucide-react'
import { cn } from '@/modules/ui/lib'

// Dados de vídeo - implementação da API será adicionada posteriormente
interface VideoItem {
  id: string
  title: string
  thumbnail: string
  duration: string
  uploadDate: string
  size: string
  url: string
  status: 'processing' | 'ready' | 'error'
}

const mockVideos: VideoItem[] = [
  {
    id: '1',
    title: 'Introdução ao Curso',
    thumbnail: 'https://via.placeholder.com/320x180',
    duration: '15:30',
    uploadDate: '2024-01-15',
    size: '125 MB',
    url: 'https://video.bunnycdn.com/play/123/abc',
    status: 'ready'
  },
  {
    id: '2',
    title: 'Configuração do Ambiente',
    thumbnail: 'https://via.placeholder.com/320x180',
    duration: '22:45',
    uploadDate: '2024-01-14',
    size: '180 MB',
    url: 'https://video.bunnycdn.com/play/123/def',
    status: 'ready'
  },
  {
    id: '3',
    title: 'Primeiros Passos',
    thumbnail: 'https://via.placeholder.com/320x180',
    duration: '18:20',
    uploadDate: '2024-01-13',
    size: '150 MB',
    url: 'https://video.bunnycdn.com/play/123/ghi',
    status: 'processing'
  }
]

export function VideoLibrary({
  isOpen,
  isMinimized,
  onClose,
  onMinimize,
  onSelectVideo
}: VideoLibraryProps) {
  const [videos, setVideos] = useState<VideoItem[]>(mockVideos)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedVideo, setSelectedVideo] = useState<VideoItem | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  const filteredVideos = videos.filter(video =>
    video.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleVideoSelect = (video: VideoItem) => {
    if (video.status === 'ready') {
      setSelectedVideo(video)
    }
  }

  const handleConfirmSelection = () => {
    if (selectedVideo && onSelectVideo) {
      onSelectVideo({
        url: selectedVideo.url,
        thumbnail: selectedVideo.thumbnail,
        duration: selectedVideo.duration,
        title: selectedVideo.title
      })
      onClose()
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Simulate upload process
    setIsUploading(true)
    setUploadProgress(0)

    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsUploading(false)
          // Add new video to list
          const newVideo: VideoItem = {
            id: Date.now().toString(),
            title: file.name.replace(/\.[^/.]+$/, ''),
            thumbnail: 'https://via.placeholder.com/320x180',
            duration: '00:00',
            uploadDate: new Date().toISOString().split('T')[0],
            size: `${Math.round(file.size / 1024 / 1024)} MB`,
            url: '',
            status: 'processing'
          }
          setVideos(prev => [newVideo, ...prev])
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  if (!isOpen) return null

  const content = (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Video className="h-5 w-5 text-[#36B37E]" />
          <h2 className="font-semibold text-gray-900">Biblioteca de Vídeos</h2>
          <Badge status="info" className="text-xs">
            {videos.length} vídeos
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onMinimize}
            className="h-8 w-8 p-0"
          >
            {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Controls */}
          <div className="p-4 border-b space-y-3">
            {/* Search and Upload */}
            <div className="flex gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar vídeos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="relative">
                <input
                  type="file"
                  accept="video/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="video-upload"
                  disabled={isUploading}
                />
                <label htmlFor="video-upload">
                  <Button
                    asChild
                    disabled={isUploading}
                    className="bg-[#36B37E] hover:bg-[#2a8f66]"
                  >
                    <span>
                      <Upload className="h-4 w-4 mr-2" />
                      {isUploading ? `${uploadProgress}%` : 'Upload'}
                    </span>
                  </Button>
                </label>
              </div>
            </div>

            {/* View Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <p className="text-sm text-gray-600">
                {filteredVideos.length} de {videos.length} vídeos
              </p>
            </div>
          </div>

          {/* Video List */}
          <ScrollArea className="flex-1 p-4">
            {filteredVideos.length === 0 ? (
              <div className="text-center py-12">
                <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Nenhum vídeo encontrado</p>
                <p className="text-sm text-gray-500">Faça upload do seu primeiro vídeo</p>
              </div>
            ) : (
              <div className={cn(
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                  : 'space-y-3'
              )}>
                {filteredVideos.map((video) => (
                  <Card
                    key={video.id}
                    className={cn(
                      'cursor-pointer transition-all hover:shadow-md',
                      selectedVideo?.id === video.id && 'ring-2 ring-[#36B37E]',
                      video.status !== 'ready' && 'opacity-60 cursor-not-allowed'
                    )}
                    onClick={() => handleVideoSelect(video)}
                  >
                    <CardContent className={cn(
                      'p-3',
                      viewMode === 'list' && 'flex items-center gap-4'
                    )}>
                      {/* Thumbnail */}
                      <div className={cn(
                        'relative rounded overflow-hidden bg-gray-100',
                        viewMode === 'grid' ? 'aspect-video mb-3' : 'w-24 h-14 flex-shrink-0'
                      )}>
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                          <Play className="h-6 w-6 text-white" />
                        </div>

                        {/* Status Badge */}
                        <div className="absolute top-2 right-2">
                          <Badge
                            status={video.status === 'ready' ? 'success' : video.status === 'processing' ? 'warning' : 'error'}
                            className="text-xs"
                          >
                            {video.status === 'ready' && 'Pronto'}
                            {video.status === 'processing' && 'Processando'}
                            {video.status === 'error' && 'Erro'}
                          </Badge>
                        </div>
                      </div>

                      {/* Video Info */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate mb-1">
                          {video.title}
                        </h4>

                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {video.duration}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(video.uploadDate).toLocaleDateString('pt-BR')}
                          </div>
                          <span>{video.size}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>

          {/* Footer */}
          {selectedVideo && (
            <div className="p-4 border-t bg-gray-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{selectedVideo.title}</p>
                  <p className="text-sm text-gray-600">Duração: {selectedVideo.duration}</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setSelectedVideo(null)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleConfirmSelection}
                    className="bg-[#36B37E] hover:bg-[#2a8f66]"
                  >
                    Usar Este Vídeo
                  </Button>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] p-0">
        {content}
      </DialogContent>
    </Dialog>
  )
}
