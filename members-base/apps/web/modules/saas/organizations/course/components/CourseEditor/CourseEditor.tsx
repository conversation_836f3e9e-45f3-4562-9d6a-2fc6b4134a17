'use client'

import { useState } from 'react'
import { CourseEditorProps, Module } from '../../types'
import { useCourseEditor } from '../../hooks/useCourseEditor'
import { CourseHeader } from './CourseHeader'
import { ModuleList } from './ModuleList'
import { VideoLibrary } from './VideoLibrary'
import { Button } from '@/modules/ui/components/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/modules/ui/components/tabs'
import { Building2, Minimize2, Plus } from 'lucide-react'
import { Skeleton } from '@/modules/ui/components/skeleton'

export function CourseEditor({ courseId, organizationSlug, mode }: CourseEditorProps) {
  const [showVideoLibrary, setShowVideoLibrary] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)

  const {
    course,
    modules,
    isLoading,
    isSaving,
    addModule: addModuleHook,
    updateModule: updateModuleHook,
    deleteModule,
    addLesson,
    updateLesson,
    deleteLesson,
    reorderModules,
    reorderLessons,
    saveCourse
  } = useCourseEditor({ courseId, organizationSlug })

  // Wrapper functions to match ModuleListProps interface
  const handleAddModule = () => {
    addModuleHook({ name: 'Novo Módulo' })
  }

  const handleEditModule = (module: Module) => {
    updateModuleHook(module.id, module)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-64 w-full" />
        <div className="flex gap-4">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-28" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (!course) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Curso não encontrado</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="content" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">Conteúdo</TabsTrigger>
          <TabsTrigger value="students">Estudantes</TabsTrigger>
          <TabsTrigger value="settings">Configurações</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-6">
          <CourseHeader
            course={course}
            onEdit={() => {}}
            onSave={saveCourse}
          />

          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => setShowVideoLibrary(true)}
              className="flex items-center gap-2"
            >
              <Building2 className="h-4 w-4" />
              Biblioteca De Vídeos
            </Button>

            <Button
              variant="outline"
              onClick={() => setIsMinimized(!isMinimized)}
              className="flex items-center gap-2"
            >
              <Minimize2 className="h-4 w-4" />
              {isMinimized ? 'Expandir' : 'Minimizar'}
            </Button>

            <Button
              onClick={handleAddModule}
              className="flex items-center gap-2 bg-[#36B37E] hover:bg-[#2a8f66]"
              disabled={isSaving}
            >
              <Plus className="h-4 w-4" />
              Adicionar
            </Button>
          </div>

          {!isMinimized && (
            <ModuleList
              modules={modules}
              onAddModule={handleAddModule}
              onEditModule={handleEditModule}
              onDeleteModule={deleteModule}
              onReorderModules={reorderModules}
            />
          )}
        </TabsContent>

        <TabsContent value="students">
          <div className="bg-white rounded-lg border p-6">
            <p className="text-gray-500">Funcionalidade em desenvolvimento...</p>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <div className="bg-white rounded-lg border p-6">
            <p className="text-gray-500">Funcionalidade em desenvolvimento...</p>
          </div>
        </TabsContent>
      </Tabs>

      {showVideoLibrary && (
        <VideoLibrary
          isOpen={showVideoLibrary}
          isMinimized={isMinimized}
          onSelectVideo={(video) => {
            console.log('Video selected:', video)
            setShowVideoLibrary(false)
          }}
          onClose={() => setShowVideoLibrary(false)}
          onMinimize={() => setIsMinimized(!isMinimized)}
        />
      )}
    </div>
  )
}
