'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/modules/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Skeleton } from '@/modules/ui/components/skeleton'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/modules/ui/components/tabs'
import { Badge } from '@/modules/ui/components/badge'
import { Textarea } from '@/modules/ui/components/textarea'
import {
  MessageCircle,
  Download,
  FileText,
  Send,
  User,
  Calendar,
  Clock
} from 'lucide-react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { Lesson, LessonComment, LessonFile } from '../types'

interface LessonContentProps {
  lesson?: Lesson
  comments: LessonComment[]
  files: LessonFile[]
  isLoadingComments: boolean
  isLoadingFiles: boolean
  onAddComment?: (comment: string) => void
  onDownloadFile?: (file: LessonFile) => void
}

export function LessonContent({
  lesson,
  comments,
  files,
  isLoadingComments,
  isLoadingFiles,
  onAddComment,
  onDownloadFile
}: LessonContentProps) {
  const [newComment, setNewComment] = useState('')
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !onAddComment) return

    setIsSubmittingComment(true)
    try {
      await onAddComment(newComment.trim())
      setNewComment('')
    } catch (error) {
      console.error('Error adding comment:', error)
    } finally {
      setIsSubmittingComment(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileIcon = (fileName?: string) => {
    if (!fileName) return FileText
    const extension = fileName.split('.').pop()?.toLowerCase()

    switch (extension) {
      case 'pdf':
        return FileText
      case 'zip':
      case 'rar':
        return Download
      default:
        return FileText
    }
  }

  return (
    <div className="space-y-6 max-w-full">
      {/* Lesson Info */}
      {lesson && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2 min-w-0 flex-1">
                  <CardTitle className="text-2xl break-words">
                    {lesson.name}
                  </CardTitle>
                  {lesson.description && (
                    <CardDescription className="text-base break-words">
                      {lesson.description}
                    </CardDescription>
                  )}
                </div>

                <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                  {lesson.userWatchedLessons?.isCompleted && (
                    <Badge status="success" className="bg-green-100 text-green-800">
                      Concluída
                    </Badge>
                  )}
                  {lesson.duration && (
                    <div className="flex items-center gap-1 px-3 py-1 text-xs border rounded-full bg-background">
                      <Clock className="h-3 w-3" />
                      {lesson.duration}
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
        </motion.div>
      )}

      {/* Tabs for Comments and Materials */}
      <Tabs defaultValue="comments" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="comments" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            Comentários ({comments.length})
          </TabsTrigger>
          <TabsTrigger value="materials" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Materiais ({files.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="comments" className="space-y-4">
          {/* Add Comment Form */}
          {onAddComment && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Adicionar Comentário</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="Escreva seu comentário sobre esta aula..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="min-h-[100px]"
                />
                <div className="flex justify-end">
                  <Button
                    onClick={handleSubmitComment}
                    disabled={!newComment.trim() || isSubmittingComment}
                    className="flex items-center gap-2"
                  >
                    <Send className="h-4 w-4" />
                    {isSubmittingComment ? 'Enviando...' : 'Enviar Comentário'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Comments List */}
          {isLoadingComments ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                      <Skeleton className="h-16 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : comments.length > 0 ? (
            <div className="space-y-4">
              {comments.map((comment) => (
                <motion.div
                  key={comment.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card>
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        {/* Comment Header */}
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                            {comment.user.image ? (
                              <img
                                src={comment.user.image}
                                alt={comment.user.name || 'User'}
                                className="w-8 h-8 rounded-full object-cover"
                              />
                            ) : (
                              <User className="w-4 h-4 text-primary" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 flex-wrap">
                              <span className="font-medium text-sm truncate">
                                {comment.user.name || 'Usuário'}
                              </span>
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                {formatDate(comment.createdAt)}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Comment Content */}
                        <div className="pl-11">
                          <p className="text-sm break-words whitespace-pre-wrap">
                            {comment.comment}
                          </p>
                        </div>

                        {/* Replies */}
                        {comment.lessonCommentReplies && comment.lessonCommentReplies.length > 0 && (
                          <div className="pl-11 space-y-3 border-l-2 border-border ml-4">
                            {comment.lessonCommentReplies.map((reply) => (
                              <div key={reply.id} className="pl-4 space-y-2">
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                                    {reply.user.image ? (
                                      <img
                                        src={reply.user.image}
                                        alt={reply.user.name || 'User'}
                                        className="w-6 h-6 rounded-full object-cover"
                                      />
                                    ) : (
                                      <User className="w-3 h-3 text-primary" />
                                    )}
                                  </div>
                                  <span className="font-medium text-xs">
                                    {reply.user.name || 'Usuário'}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {formatDate(reply.createdAt)}
                                  </span>
                                </div>
                                <p className="text-xs break-words whitespace-pre-wrap pl-8">
                                  {reply.replyComment}
                                </p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Ainda não há comentários nesta aula.
                </p>
                {onAddComment && (
                  <p className="text-sm text-muted-foreground mt-2">
                    Seja o primeiro a comentar!
                  </p>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="materials" className="space-y-4">
          {isLoadingFiles ? (
            <div className="space-y-4">
              {[...Array(2)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                      <Skeleton className="h-9 w-20" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : files.length > 0 ? (
            <div className="space-y-4">
              {files.map((file) => {
                const FileIcon = getFileIcon(file.file)

                return (
                  <motion.div
                    key={file.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3 min-w-0 flex-1">
                            <div className="w-10 h-10 rounded bg-primary/10 flex items-center justify-center flex-shrink-0">
                              <FileIcon className="h-5 w-5 text-primary" />
                            </div>
                            <div className="min-w-0 flex-1">
                              <p className="font-medium truncate">
                                {file.title}
                              </p>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                {file.fileSize && (
                                  <span>{file.fileSize}</span>
                                )}
                                <span>•</span>
                                <span>{formatDate(file.createdAt)}</span>
                              </div>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-shrink-0 ml-4"
                            onClick={() => onDownloadFile?.(file)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Baixar
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Download className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Não há materiais disponíveis para esta aula.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
