import type { OrganizationMetadata } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery } from "@tanstack/react-query";

// Query keys for organizations
export const organizationsQueryKey = ["organizations"];
export const organizationListQueryKey = ["organizations", "list"];
export const fullOrganizationQueryKey = (organizationId: string) => ["organizations", organizationId];

// Hook to get full organization details
export const useFullOrganizationQuery = (organizationId: string) => {
	return useQuery({
		queryKey: fullOrganizationQueryKey(organizationId),
		queryFn: async () => {
			const { data, error } = await authClient.organization.getFullOrganization({
				query: {
					organizationId,
				},
			});

			if (error) {
				throw new Error(error.message || "Failed to fetch organization");
			}

			return data;
		},
		enabled: !!organizationId,
	});
};

// API functions for organizations
export const getOrganizations = async () => {
  const response = await fetch("/api/organizations/user");
  if (!response.ok) {
    throw new Error("Failed to fetch organizations");
  }
  return response.json();
};

export const getOrganization = async (organizationId: string) => {
  const response = await fetch(`/api/organizations/${organizationId}`);
  if (!response.ok) {
    throw new Error("Failed to fetch organization");
  }
  return response.json();
};

export const createOrganization = async (data: {
  name: string;
  description?: string;
  logo?: string;
}) => {
  const response = await fetch("/api/organizations", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to create organization");
  }
  return response.json();
};

export const updateOrganization = async (
  organizationId: string,
  data: {
    name?: string;
    description?: string;
    logo?: string;
  }
) => {
  const response = await fetch(`/api/organizations/${organizationId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to update organization");
  }
  return response.json();
};

export const deleteOrganization = async (organizationId: string) => {
  const response = await fetch(`/api/organizations/${organizationId}`, {
    method: "DELETE",
  });
  if (!response.ok) {
    throw new Error("Failed to delete organization");
  }
  return response.json();
};

export const activeOrganizationQueryKey = (slug: string) =>
	["user", "activeOrganization", slug] as const;
export const useActiveOrganizationQuery = (
	slug: string,
	options?: {
		enabled?: boolean;
	},
) => {
	return useQuery({
		queryKey: activeOrganizationQueryKey(slug),
		queryFn: async () => {
			const { data, error } =
				await authClient.organization.getFullOrganization({
					query: {
						organizationSlug: slug,
					},
				});

			if (error) {
				throw new Error(
					error.message || "Failed to fetch active organization",
				);
			}

			return data;
		},
		enabled: options?.enabled,
	});
};

export const generateOrganizationSlug = async (name: string) => {
	const response = await apiClient.organizations["generate-slug"].$get({
		query: {
			name,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to generate organization slug");
	}

	const { slug } = await response.json();

	return slug;
};

// Hook to get list of organizations
export const useOrganizationListQuery = () => {
	return useQuery({
		queryKey: organizationListQueryKey,
		queryFn: async () => {
			const { data, error } = await authClient.organization.list();

			if (error) {
				throw new Error(error.message || "Failed to fetch organizations");
			}

			return data;
		},
	});
};

/*
 * Create organization
 */
export const createOrganizationMutationKey = ["create-organization"] as const;
export const useCreateOrganizationMutation = () => {
	return useMutation({
		mutationKey: createOrganizationMutationKey,
		mutationFn: async ({
			name,
			metadata,
		}: {
			name: string;
			metadata?: OrganizationMetadata;
		}) => {
			const { error, data } = await authClient.organization.create({
				name,
				slug: await generateOrganizationSlug(name),
				metadata,
			});

			if (error) {
				throw error;
			}

			return data;
		},
	});
};

/*
 * Update organization
 */
export const updateOrganizationMutationKey = ["update-organization"] as const;
export const useUpdateOrganizationMutation = () => {
	return useMutation({
		mutationKey: updateOrganizationMutationKey,
		mutationFn: async ({
			id,
			name,
			metadata,
			updateSlug,
		}: {
			id: string;
			name: string;
			metadata?: OrganizationMetadata;
			updateSlug?: boolean;
		}) => {
			const { error, data } = await authClient.organization.update({
				organizationId: id,
				data: {
					name,
					slug: updateSlug
						? await generateOrganizationSlug(name)
						: undefined,
					metadata,
				},
			});

			if (error) {
				throw error;
			}

			return data;
		},
	});
};

export const memberAreaSettingsQueryKey = (organizationId: string) => [
	"memberAreaSettings",
	organizationId,
];

export function useMemberAreaSettingsQuery(organizationId: string) {
	return useQuery({
		queryKey: memberAreaSettingsQueryKey(organizationId),
		queryFn: async () => {
			const response = await fetch("/api/member-area-settings");

			if (!response.ok) {
				throw new Error("Failed to fetch member area settings");
			}

			return response.json();
		},
	});
}

export function useUpdateMemberAreaSettingsMutation() {
	return useMutation({
		mutationFn: async ({
			organizationId,
			data,
		}: {
			organizationId: string;
			data: any;
		}) => {
			const response = await fetch("/api/member-area-settings", {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(data),
			});

			if (!response.ok) {
				throw new Error("Failed to update member area settings");
			}

			return response.json();
		},
	});
}
