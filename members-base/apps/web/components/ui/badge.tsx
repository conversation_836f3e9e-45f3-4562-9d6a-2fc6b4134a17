import { cn } from "../../lib/utils";
import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type React from "react";

export const badge = cva(
	[
		"inline-block",
		"rounded-full",
		"px-3",
		"py-1",
		"text-xs",
		"font-medium",
		"leading-tight",
		"border",
	],
	{
		variants: {
			status: {
				success: ["bg-success/20", "text-success", "border-success/30"],
				info: ["bg-primary/20", "text-primary", "border-primary/30"],
				warning: ["bg-highlight/20", "text-highlight", "border-highlight/30"],
				error: ["bg-destructive/20", "text-destructive", "border-destructive/30"],
				muted: ["bg-muted-foreground/20", "text-muted-foreground", "border-muted-foreground/30"],
			},
		},
		defaultVariants: {
			status: "info",
		},
	},
);

export type BadgeProps = React.HtmlHTMLAttributes<HTMLDivElement> &
	VariantProps<typeof badge>;

export const Badge = ({
	children,
	className,
	status,
	...props
}: BadgeProps) => (
	<span className={cn(badge({ status }), className)} {...props}>
		{children}
	</span>
);

Badge.displayName = "Badge";
