# 🚀 Configuração de Variáveis de Ambiente - Produção

Este documento contém as variáveis de ambiente necessárias para o funcionamento correto do sistema em produção, incluindo SSO, login com email/senha e magic link.

## 📋 Variáveis de Ambiente de Produção

```bash
# ===========================================
# BETTER AUTH - Autenticação
# ===========================================
BETTER_AUTH_SECRET=112c209614f7bb08cf834f53d5e2b1721ba940a106b01a3769a4e9bd29cd723c697ce245a874c1ab3140142c3a4fb780c19bf1c5a302b1ee3e85a4950cc3e6c8

# ===========================================
# DATABASE - Banco de Dados
# ===========================================
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# ===========================================
# EMAIL - Configuração SMTP (CORRIGIDO)
# ===========================================
# Variáveis principais (usadas pelo Better Auth)
SMTP_HOST=smtp.mailgun.org
SMTP_USER=<EMAIL>
SMTP_PASS=**************************************************
SMTP_PORT=587
SMTP_SECURE=false

# Variáveis legacy (manter para compatibilidade)
MAIL_HOST=smtp.mailgun.org
MAIL_USER=<EMAIL>
MAIL_PASS=**************************************************
MAIL_PORT=587
MAILER_SMTP_SECURE=false
MAIL_USE_CUSTOM_CONFIGS=true
MAIL__ADDRESS_FROM=<EMAIL>
MAIL__SMTP_ENABLE=true
MAIL__TLS_REJECT_UNAUTHORIZED=false

# ===========================================
# URLs E DOMÍNIOS
# ===========================================
NEXT_PUBLIC_SITE_URL=https://caktomembers.cloud.cakto.app
NEXT_PUBLIC_APP_URL=https://caktomembers.cloud.cakto.app
NODE_ENV=production

# ===========================================
# SSO - Single Sign-On (CORRIGIDO)
# ===========================================
# Backend SSO Configuration
CAKTO_API_URL=https://sso.cakto.com.br
CAKTO_CLIENT_ID=Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN
CAKTO_CLIENT_SECRET=KqLDBTCThIh82h2kOPRK7VBbpVyAOoQSTXSJDmdOeSvO5HeLrMX6PeuesRK6qcZGZJ8aoYcLOxy9htHzmBo57O1yPdcCXCCNlolJMt7P1NyiSs8ePOgT0NJXsjIT6O9f
CAKTO_DOMAIN=.cakto.com.br
SSO_DOMAIN=sso.cakto.com.br

# Frontend SSO Configuration
NEXT_PUBLIC_CAKTO_API_URL=https://sso.cakto.com.br
NEXT_PUBLIC_CAKTO_CLIENT_ID=Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN
NEXT_PUBLIC_CAKTO_BACKEND_URL=https://api.cakto.com.br

# ===========================================
# STORAGE - DigitalOcean Spaces
# ===========================================
S3_ENDPOINT=https://cakto-members-files.nyc3.digitaloceanspaces.com/
S3_REGION=nyc3
S3_ACCESS_KEY_ID=DO00CZT23AZED2ZHJTL7
S3_SECRET_ACCESS_KEY=cqLzBLc1bmP5HjTiStDz4RGTolwn/DKWn5xW//QKh+c
S3_BUCKET_NAME=cakto-members-files
NEXT_PUBLIC_AVATARS_BUCKET_NAME=cakto-members-files

# ===========================================
# BUNNY.NET - CDN
# ===========================================
NEXT_PUBLIC_BUNNY_LIBRARY_ID=467287
NEXT_PUBLIC_BUNNY_SIGNATURE=812c6dae-88a0-4b5a-bf32b440cae9-5072-4772
NEXT_PUBLIC_BUNNY_EXPIRE=1735689600

# ===========================================
# CONFIGURAÇÕES DE PRODUÇÃO (CORRIGIDO)
# ===========================================
USE_CAKTO_MOCK=false
```

## 🔧 Correções Aplicadas

### ❌ **Problemas Identificados e Corrigidos:**

1. **Variáveis SMTP Incorretas**
   - ❌ **Problema:** Código esperava `SMTP_HOST`, `SMTP_USER`, `SMTP_PASS`
   - ❌ **Estado:** Apenas `MAIL_HOST`, `MAIL_USER`, `MAIL_PASS` definidas
   - ✅ **Correção:** Adicionadas variáveis `SMTP_*` corretas

2. **SSO_REDIRECT_URI com localhost**
   - ❌ **Problema:** `SSO_REDIRECT_URI=http://localhost:3000/auth/callback`
   - ✅ **Correção:** `SSO_REDIRECT_URI=https://caktomembers.cloud.cakto.app/auth/sso/callback`

3. **USE_CAKTO_MOCK em Produção**
   - ❌ **Problema:** `USE_CAKTO_MOCK=true`
   - ✅ **Correção:** `USE_CAKTO_MOCK=false`

4. **Falta de SSO_REDIRECT_URI**
   - ❌ **Problema:** Variável não estava definida
   - ✅ **Correção:** Adicionada com URL de produção

## 🎯 Funcionalidades Garantidas

### ✅ **Login com Email e Senha**
- `emailAndPassword: { enabled: true }` no Better Auth
- Endpoint `/api/auth/sign-in/email` funcionando
- Variáveis SMTP corretas para envio de emails

### ✅ **Magic Link**
- Plugin `magicLink` configurado
- `trustedOrigins` inclui domínio de produção
- `onSuccess` hook para redirecionamentos corretos

### ✅ **SSO (Single Sign-On)**
- Plugin `genericOAuth` configurado
- `providerId: "django-sso"` funcionando
- URLs de callback corretas para produção

### ✅ **Trusted Origins**
```typescript
trustedOrigins: [
  "https://caktomembers.cloud.cakto.app",
  "http://localhost:3000",
  "https://localhost:3000"
]
```

## 📝 Como Aplicar

### 1. **Copiar as Variáveis**
Copie todas as variáveis acima para o seu ambiente de produção.

### 2. **Verificar Configurações**
Execute o script de verificação:
```bash
npx tsx scripts/check-production-env.ts
```

### 3. **Testar Funcionalidades**
- ✅ Login com email/senha: `/api/auth/sign-in/email`
- ✅ Magic link: `/api/auth/magic-link/verify`
- ✅ SSO: `/api/auth/oauth2/django-sso`

## 🔍 Verificação de Funcionamento

### **Endpoints que devem funcionar:**
```bash
# Login com email/senha
POST https://caktomembers.cloud.cakto.app/api/auth/sign-in/email

# Magic link
GET https://caktomembers.cloud.cakto.app/api/auth/magic-link/verify?token=TOKEN&callbackURL=/app

# SSO
GET https://caktomembers.cloud.cakto.app/api/auth/oauth2/django-sso
```

### **Respostas esperadas:**
- ✅ `401` para credenciais inválidas (login)
- ✅ `302` para redirecionamentos (magic link)
- ✅ `200` para autenticação bem-sucedida

## 🚨 Importante

1. **Nunca commitar** estas variáveis no Git
2. **Manter backup** das variáveis em local seguro
3. **Rotacionar** `BETTER_AUTH_SECRET` periodicamente
4. **Monitorar** logs para erros de autenticação

## 📞 Suporte

Em caso de problemas:
1. Verificar logs do Better Auth
2. Testar endpoints individualmente
3. Verificar configuração de DNS e SSL
4. Confirmar conectividade com banco de dados
