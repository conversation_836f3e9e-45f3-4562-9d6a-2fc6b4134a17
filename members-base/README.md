# 🚀 Cakto Members

Uma plataforma SaaS moderna e completa para gerenciamento de membros e cursos, construída com Next.js 15, Better Auth, e arquitetura de monorepo.

## ✨ Características

- 🔐 **Autenticação Completa** - Better Auth com SSO, magic links e 2FA
- 🏢 **Gestão de Organizações** - Multi-tenant com controle de membros
- 📚 **Sistema de Cursos** - LMS completo com módulos e lições
- 🛍️ **Vitrines Personalizáveis** - Páginas de vendas com seções e cursos
- 💳 **Integração de Pagamentos** - Webhooks e produtos Cakto
- 📧 **Sistema de Email** - Templates responsivos com múltiplos provedores
- 🎨 **UI Moderna** - Tailwind CSS com shadcn/ui
- 🌍 **Internacionalização** - Suporte a múltiplos idiomas
- 🔒 **Segurança** - Sessões seguras, rate limiting e validação
- 📱 **Responsivo** - Design mobile-first
- ⚡ **Performance** - Next.js 15 com App Router e Turbo
- 🗄️ **Banco de Dados** - PostgreSQL com Prisma ORM

## 🛠️ Stack Tecnológica

### Frontend
- **Next.js 15** - Framework React com App Router
- **TypeScript** - Tipagem estática
- **Tailwind CSS 4** - Framework CSS utilitário
- **shadcn/ui** - Componentes UI modernos
- **TanStack Query** - Gerenciamento de estado do servidor
- **Framer Motion** - Animações fluidas

### Backend
- **Better Auth 1.2.8** - Sistema de autenticação completo
- **Prisma 6.9** - ORM para PostgreSQL
- **Hono** - Framework web rápido
- **Zod** - Validação de schemas
- **Fastify** - Servidor HTTP

### Infraestrutura
- **PostgreSQL** - Banco de dados principal
- **Redis** - Cache e sessões
- **Docker** - Containerização
- **Turbo** - Build system para monorepo
- **Biome** - Linter e formatter

## 🚀 Início Rápido

### Pré-requisitos

- **Node.js** 20+
- **pnpm** 9.3.0+
- **Docker** e **Docker Compose**
- **Git**

### Instalação

1. **Clone o repositório**
   ```bash
   git clone <repository-url>
   cd cakto-members
   ```

2. **Configure as variáveis de ambiente**
   ```bash
   cp env-production.txt .env.local
   # Edite o arquivo .env.local conforme necessário
   ```

3. **Inicie os serviços Docker**
   ```bash
   docker-compose up -d
   ```

4. **Instale as dependências**
   ```bash
   pnpm install
   ```

5. **Configure o banco de dados**
   ```bash
   pnpm db:push
   ```

6. **Inicie o servidor de desenvolvimento**
   ```bash
   pnpm dev
   ```

7. **Acesse a aplicação**
   - **Frontend**: http://localhost:3000
   - **Prisma Studio**: http://localhost:5555

## 📁 Estrutura do Projeto

```
cakto-members/
├── apps/
│   └── web/                    # Aplicação Next.js principal
│       ├── app/                # App Router (Next.js 15)
│       ├── components/         # Componentes React
│       ├── modules/            # Módulos da aplicação
│       └── public/             # Arquivos estáticos
├── packages/
│   ├── auth/                   # Configuração Better Auth
│   ├── database/               # Schema Prisma e queries
│   ├── api/                    # Rotas da API (Hono)
│   ├── mail/                   # Sistema de email
│   ├── storage/                # Upload de arquivos (S3)
│   ├── i18n/                   # Internacionalização
│   ├── logs/                   # Sistema de logs
│   └── utils/                  # Utilitários
├── config/                     # Configurações globais
├── tooling/                    # Ferramentas de desenvolvimento
├── scripts/                    # Scripts utilitários
└── docker-compose.yml          # Serviços Docker
```

## 🔧 Configuração

### Variáveis de Ambiente Essenciais

```env
# Banco de Dados
DATABASE_URL="postgresql://cakto:cakto@localhost:5432/cakto"

# Autenticação
BETTER_AUTH_SECRET="seu_secret_aqui"

# Aplicação
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Email
MAIL__ADDRESS_FROM="<EMAIL>"
MAIL_HOST="smtp.seudominio.com"
MAIL_USER="seu_usuario"
MAIL_PASS="sua_senha"

# Storage (S3)
S3_ENDPOINT="https://seu-bucket.s3.amazonaws.com"
S3_REGION="us-east-1"
S3_ACCESS_KEY_ID="sua_access_key"
S3_SECRET_ACCESS_KEY="sua_secret_key"
S3_BUCKET_NAME="seu-bucket"
```

### Configurações Opcionais

- **SSO**: Integração com Cakto SSO
- **Email**: Resend, Nodemailer, Mailgun
- **Storage**: S3, MinIO, DigitalOcean Spaces
- **Analytics**: Google Analytics, PostHog, Mixpanel
- **Streaming**: Bunny.net

## 🎯 Funcionalidades Principais

### 👥 Gestão de Usuários
- Registro e login com múltiplos métodos
- SSO integrado com Cakto
- Magic links para login sem senha
- Autenticação de dois fatores (2FA)
- Perfis de usuário personalizáveis
- Gerenciamento de sessões

### 🏢 Organizações
- Criação e gestão de organizações
- Sistema de convites por email
- Controle de permissões (owner, admin, member)
- Logotipos e personalização
- Subdomínios e domínios customizados
- Dashboard organizacional

### 📚 Sistema de Cursos
- Criação e gestão de cursos
- Módulos e lições organizadas
- Upload de arquivos e vídeos
- Sistema de comentários
- Controle de acesso por curso
- Integração com produtos Cakto

### 🛍️ Vitrines
- Criação de páginas de vendas
- Seções organizadas com cursos
- Controle de visibilidade (público/privado)
- Preços e checkout personalizados
- Webhooks para integração
- Vitrines padrão por organização

### 💳 Pagamentos
- Integração com produtos Cakto
- Webhooks para sincronização
- Controle de acesso por compra
- Histórico de transações
- Status de pagamentos

### 📧 Comunicação
- Templates de email responsivos
- Magic links para login
- Verificação de email
- Notificações de organização
- Newsletter integrada

### 🔒 Segurança
- Sessões seguras com cookies
- Proteção CSRF
- Rate limiting
- Validação de dados com Zod
- Logs de auditoria
- Middleware de autenticação

## 🚀 Deploy

### Desenvolvimento Local
```bash
# Iniciar serviços
docker-compose up -d

# Instalar dependências
pnpm install

# Executar migrações
pnpm db:push

# Iniciar servidor
pnpm dev
```

### Produção
```bash
# Build da aplicação
pnpm build

# Iniciar servidor de produção
pnpm start
```

## 📚 Comandos Úteis

```bash
# Desenvolvimento
pnpm dev                    # Servidor de desenvolvimento
pnpm build                  # Build de produção
pnpm start                  # Servidor de produção

# Banco de dados
pnpm db:push                # Sincronizar schema
pnpm db:migrate             # Executar migrações
pnpm db:check               # Verificar compatibilidade

# Usuários
pnpm create:user            # Criar usuário
pnpm create:user:auth       # Criar usuário com Better Auth
pnpm list:users             # Listar usuários

# Testes
pnpm test:email             # Testar sistema de email
pnpm test:webhook           # Testar webhooks
pnpm test:sso               # Testar SSO
pnpm test:integration       # Testes de integração

# Qualidade de código
pnpm lint                   # Executar linter
pnpm format                 # Formatar código

# Limpeza
pnpm clean                  # Limpar builds
```

## 🧪 Testes

O projeto inclui diversos scripts de teste para validar funcionalidades:

- **Email**: Teste de templates e envio
- **Webhooks**: Validação de integrações
- **SSO**: Teste de autenticação social
- **Integração**: Testes end-to-end
- **E2E**: Testes com Playwright

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request
 
 
