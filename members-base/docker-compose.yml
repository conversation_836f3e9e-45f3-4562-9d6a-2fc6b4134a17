version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # Database
      - DATABASE_URL=${DATABASE_URL}
      # Better Auth
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      # Next.js URLs
      - NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
      # Mail SMTP
      - MAIL__ADDRESS_FROM=${MAIL__ADDRESS_FROM}
      - MAIL__SMTP_ENABLE=${MAIL__SMTP_ENABLE}
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_PORT=${MAIL_PORT}
      - MAILER_SMTP_SECURE=${MAILER_SMTP_SECURE}
      - MAIL_USER=${MAIL_USER}
      - MAIL_PASS=${MAIL_PASS}
      - MAIL__TLS_REJECT_UNAUTHORIZED=${MAIL__TLS_REJECT_UNAUTHORIZED}
      - MAIL_USE_CUSTOM_CONFIGS=${MAIL_USE_CUSTOM_CONFIGS}
      # Storage
      - S3_ENDPOINT=${S3_ENDPOINT}
      - S3_REGION=${S3_REGION}
      - S3_ACCESS_KEY_ID=${S3_ACCESS_KEY_ID}
      - S3_SECRET_ACCESS_KEY=${S3_SECRET_ACCESS_KEY}
      - NEXT_PUBLIC_AVATARS_BUCKET_NAME=${NEXT_PUBLIC_AVATARS_BUCKET_NAME}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      # SSO Configuration
      - CAKTO_SSO_CLIENT_ID=${CAKTO_SSO_CLIENT_ID}
      - CAKTO_SSO_CLIENT_SECRET=${CAKTO_SSO_CLIENT_SECRET}
      - SSO_REDIRECT_URI=${SSO_REDIRECT_URI}
      - CAKTO_API_URL=${CAKTO_API_URL}
      - SSO_DOMAIN=${SSO_DOMAIN}
      - CAKTO_DOMAIN=${CAKTO_DOMAIN}
      # Bunny.net
      - NEXT_PUBLIC_BUNNY_LIBRARY_ID=${NEXT_PUBLIC_BUNNY_LIBRARY_ID}
      - NEXT_PUBLIC_BUNNY_SIGNATURE=${NEXT_PUBLIC_BUNNY_SIGNATURE}
      - NEXT_PUBLIC_BUNNY_EXPIRE=${NEXT_PUBLIC_BUNNY_EXPIRE}
      # Cakto Backend
      - NEXT_PUBLIC_CAKTO_BACKEND_URL=${NEXT_PUBLIC_CAKTO_BACKEND_URL}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
