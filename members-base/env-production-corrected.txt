# Variáveis de Ambiente de Produção Corrigidas
# ===========================================

# Better Auth
BETTER_AUTH_SECRET=112c209614f7bb08cf834f53d5e2b1721ba940a106b01a3769a4e9bd29cd723c697ce245a874c1ab3140142c3a4fb780c19bf1c5a302b1ee3e85a4950cc3e6c8

# Database
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# Email Configuration (CORRIGIDO)
SMTP_HOST=smtp.mailgun.org
SMTP_USER=<EMAIL>
SMTP_PASS=**************************************************
SMTP_PORT=587
SMTP_SECURE=false

# Email Configuration (Legacy - manter para compatibilidade)
MAIL_HOST=smtp.mailgun.org
MAIL_USER=<EMAIL>
MAIL_PASS=**************************************************
MAIL_PORT=587
MAILER_SMTP_SECURE=false
MAIL_USE_CUSTOM_CONFIGS=true
MAIL__ADDRESS_FROM=<EMAIL>
MAIL__SMTP_ENABLE=true
MAIL__TLS_REJECT_UNAUTHORIZED=false

# URLs e Domínios
NEXT_PUBLIC_SITE_URL=https://caktomembers.cloud.cakto.app
NEXT_PUBLIC_APP_URL=https://caktomembers.cloud.cakto.app
NODE_ENV=production

# SSO Configuration (CORRIGIDO)
SSO_REDIRECT_URI=https://caktomembers.cloud.cakto.app/auth/sso/callback
CAKTO_API_URL=https://sso.cakto.com.br
CAKTO_CLIENT_ID=Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN
CAKTO_CLIENT_SECRET=KqLDBTCThIh82h2kOPRK7VBbpVyAOoQSTXSJDmdOeSvO5HeLrMX6PeuesRK6qcZGZJ8aoYcLOxy9htHzmBo57O1yPdcCXCCNlolJMt7P1NyiSs8ePOgT0NJXsjIT6O9f
CAKTO_DOMAIN=.cakto.com.br
SSO_DOMAIN=sso.cakto.com.br

# Frontend SSO Configuration
NEXT_PUBLIC_CAKTO_API_URL=https://sso.cakto.com.br
NEXT_PUBLIC_CAKTO_CLIENT_ID=Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN
NEXT_PUBLIC_CAKTO_BACKEND_URL=https://api.cakto.com.br

# Storage (DigitalOcean Spaces)
S3_ENDPOINT=https://cakto-members-files.nyc3.digitaloceanspaces.com/
S3_REGION=nyc3
S3_ACCESS_KEY_ID=DO00CZT23AZED2ZHJTL7
S3_SECRET_ACCESS_KEY=cqLzBLc1bmP5HjTiStDz4RGTolwn/DKWn5xW//QKh+c
S3_BUCKET_NAME=cakto-members-files
NEXT_PUBLIC_AVATARS_BUCKET_NAME=cakto-members-files

# Bunny.net
NEXT_PUBLIC_BUNNY_LIBRARY_ID=467287
NEXT_PUBLIC_BUNNY_SIGNATURE=812c6dae-88a0-4b5a-bf32b440cae9-5072-4772
NEXT_PUBLIC_BUNNY_EXPIRE=1735689600

# Configurações de Produção (CORRIGIDO)
USE_CAKTO_MOCK=false

# ===========================================
# RESUMO DAS CORREÇÕES:
# ===========================================
# ✅ Adicionado: SMTP_HOST, SMTP_USER, SMTP_PASS
# ✅ Corrigido: SSO_REDIRECT_URI (removido localhost)
# ✅ Corrigido: USE_CAKTO_MOCK=false
# ✅ Mantido: NODE_ENV=production
# ✅ Mantido: NEXT_PUBLIC_SITE_URL correto
# ===========================================
