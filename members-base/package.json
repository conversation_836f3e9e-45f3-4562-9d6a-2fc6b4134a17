{"name": "cakto-members", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write", "db:push": "pnpm --filter database push", "test:email": "dotenv -c -e .env.local -- tsx scripts/test-email.ts", "test:webhook:complete": "dotenv -c -e .env.local -- tsx scripts/test-cakto-webhook-complete.ts", "test:magic-link": "dotenv -c -e .env.local -- tsx scripts/test-magic-link-welcome.ts", "remove:user-access": "dotenv -c -e .env.local -- tsx scripts/remove-user-access.ts", "remove:access": "dotenv -c -e .env.local -- tsx scripts/remove-course-access.ts"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.15.30", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "turbo": "^2.5.4", "typescript": "5.8.3"}, "dependencies": {"decimal.js": "^10.6.0"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}}