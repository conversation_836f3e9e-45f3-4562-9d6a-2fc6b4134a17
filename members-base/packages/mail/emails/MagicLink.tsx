import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface MagicLinkProps {
  name?: string;
  url: string;
}

export const MagicLink = ({ name, url }: MagicLinkProps) => (
  <Wrapper>
    <Preview>Login seguro | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Acesse sua conta
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        Clique no botão abaixo para fazer login na sua conta Cakto Members.<br />
        Este link é válido por 10 minutos e pode ser usado apenas uma vez.
      </Text>
      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#0F7864",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Fazer Login
        </Link>
      </Section>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Se você não solicitou este acesso, ignore este email.<br />
        Seu login permanecerá seguro.
      </Text>
    </Section>
  </Wrapper>
);

export default MagicLink;
