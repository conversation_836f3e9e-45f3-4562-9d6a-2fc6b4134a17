import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface OrganizationInvitationProps {
  name?: string;
  organizationName: string;
  url: string;
}

export const OrganizationInvitation = ({
  name,
  organizationName,
  url
}: OrganizationInvitationProps) => (
  <Wrapper>
    <Preview>Convite para {organizationName} | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Você foi convidado para {organizationName}
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        Você recebeu um convite para participar da organização <strong>{organizationName}</strong>.<br />
        Clique no botão abaixo para aceitar o convite e começar a colaborar.
      </Text>
      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#0F7864",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Aceitar Convite
        </Link>
      </Section>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Se você não conhece esta organização, ignore este email.
      </Text>
    </Section>
  </Wrapper>
);

export default OrganizationInvitation;
