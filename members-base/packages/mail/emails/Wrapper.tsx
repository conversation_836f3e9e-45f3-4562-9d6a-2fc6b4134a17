import React from "react";
import { Head, Body, Container, Section, Text } from "@react-email/components";
import { Logo } from "../src/components/Logo";

export const Wrapper = ({ children }: { children: React.ReactNode }) => {

  return (
    <>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={{ textAlign: "center", marginBottom: "32px" }}>
            <Logo />
          </Section>
          {children}
          <Section style={{ marginTop: 40, textAlign: "center", color: "#888", fontSize: 12 }}>
            <Text style={{ margin: 0 }}>
              Atenciosamente,<br />Equipe Cakto Members
            </Text>
          </Section>
          <Section style={{
            marginTop: 24,
            padding: "16px",
            backgroundColor: "#f8f9fa",
            borderRadius: "8px",
            textAlign: "center",
            color: "#666",
            fontSize: 11
          }}>
            <Text style={{ margin: "0 0 4px 0", fontWeight: "600" }}>
              Plataforma Cakto
            </Text>
            <Text style={{ margin: "2px 0" }}>
              Av <PERSON> 379, <PERSON><PERSON><PERSON> 1º andar
            </Text>
            <Text style={{ margin: "2px 0" }}>
              CEP 88330-696, Balneário Camboriú - SC
            </Text>
          </Section>
        </Container>
      </Body>
    </>
  );
};

const main = {
  backgroundColor: "#f8f9fb",
  fontFamily: "HelveticaNeue,Helvetica,Arial,sans-serif",
};

const container = {
  backgroundColor: "#fff",
  border: "1px solid #eee",
  borderRadius: "8px",
  boxShadow: "0 5px 10px rgba(20,50,70,.08)",
  maxWidth: "600px",
  margin: "40px auto",
  padding: "32px 24px 24px",
};
