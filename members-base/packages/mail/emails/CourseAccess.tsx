import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface CourseAccessEmailProps {
  name: string;
  courseName: string;
  url: string;
}

export const CourseAccessEmail = ({
  name,
  courseName,
  url,
}: CourseAccessEmailProps) => (
  <Wrapper>
    <Preview>Acesso liberado para {courseName} | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Acesso Liberado ao Curso!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá {name},<br />
        Seu acesso ao curso <strong>{courseName}</strong> foi liberado com sucesso!<br />
        Clique no botão abaixo para começar a estudar agora mesmo.
      </Text>
      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#0F7864",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Acessar Curso
        </Link>
      </Section>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Bons estudos!<br />
        Se tiver dúvidas, conte com nosso suporte.
      </Text>
    </Section>
  </Wrapper>
);

export default CourseAccessEmail;
