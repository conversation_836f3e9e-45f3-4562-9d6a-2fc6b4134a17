{"admin": {"menu": {"organizations": "Workspaces", "users": "Usuários"}, "organizations": {"backToList": "Voltar para workspaces", "confirmDelete": {"confirm": "Excluir", "message": "Tem certeza de que deseja excluir este workspace? Esta ação não pode ser desfeita.", "title": "Excluir workspace"}, "create": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "deleteOrganization": {"deleted": "Workspace foi excluído com sucesso!", "deleting": "Excluindo workspace...", "notDeleted": "Workspace não pôde ser excluído. Tente novamente."}, "edit": "<PERSON><PERSON>", "form": {"createTitle": "Criar um workspace", "name": "Nome do workspace", "notifications": {"error": "Não foi possível salvar o workspace. Tente novamente mais tarde.", "success": "Workspace foi salvo."}, "save": "<PERSON><PERSON>", "updateTitle": "Editar workspace"}, "loading": "Carregando workspaces...", "membersCount": "{count} {count, plural, one {membro} other {membros}}", "search": "Pesquisar por um workspace...", "title": "Gerenciar workspaces"}, "title": "Administração", "users": {"confirmDelete": {"confirm": "Excluir", "message": "Tem certeza de que deseja excluir este usuário? Esta ação não pode ser desfeita.", "title": "Excluir usuário"}, "delete": "Excluir", "deleteUser": {"deleted": "Usuário foi excluído com sucesso!", "deleting": "Excluindo usuário...", "notDeleted": "Usuário não pôde ser excluído. Tente novamente."}, "emailVerified": {"verified": "<PERSON>ail verificado", "waiting": "Email aguardando verificação"}, "impersonate": "Personificar", "impersonation": {"impersonating": "Personificando como {name}..."}, "loading": "Carregando usuários...", "resendVerificationMail": {"error": "Não foi possível reenviar o email de verificação. Tente novamente.", "submitting": "Reenviando email de verificação...", "success": "Email de verificação foi enviado.", "title": "Reenviar email de verificação"}, "search": "Pesquisar por nome ou email...", "title": "Gerenciar usuários", "assignAdminRole": "Atribuir função de administrador", "removeAdminRole": "Remover função de administrador"}, "description": "Gerencie sua aplicação."}, "app": {"menu": {"accountSettings": "Configurações da conta", "admin": "Admin", "aiChatbot": "Chatbot IA", "courses": "Cursos", "vitrines": "Vitrines", "members": "Me<PERSON><PERSON>", "organizationSettings": "Configurações", "start": "Início"}, "userMenu": {"accountSettings": "Configurações da conta", "colorMode": "<PERSON><PERSON> de <PERSON>", "documentation": "Documentação", "home": "Início", "logout": "<PERSON><PERSON>"}}, "auth": {"errors": {"invalidEmailOrPassword": "As credenciais que você inseriu são inválidas. Verifique-as e tente novamente.", "unknown": "Algo deu errado. Tente novamente.", "userNotFound": "Este usuário não existe", "failedToCreateUser": "Não foi possível criar o usuário. Tente novamente.", "failedToCreateSession": "Não foi possível criar uma sessão. Tente novamente.", "failedToUpdateUser": "Não foi possível atualizar o usuário. Tente novamente.", "failedToGetSession": "Não foi possível obter a sessão.", "invalidPassword": "A senha inserida está incorreta.", "invalidEmail": "O email inserido é inválido.", "invalidToken": "O token que você inseriu é inválido ou expirou.", "credentialAccountNotFound": "Conta não encontrada.", "emailCanNotBeUpdated": "Email não pôde ser atualizado. Tente novamente.", "emailNotVerified": "Por favor, verifique seu email primeiro antes de fazer login.", "failedToGetUserInfo": "Não foi possível carregar as informações do usuário.", "idTokenNotSupported": "Token ID não é suportado.", "passwordTooLong": "Senha é muito longa.", "passwordTooShort": "Senha é muito curta.", "providerNotFound": "Este provedor não é suportado.", "socialAccountAlreadyLinked": "Esta conta já está vinculada a um usuário.", "userEmailNotFound": "Email não encontrado.", "userAlreadyExists": "Este usuário já existe.", "invalidInvitation": "O convite é inválido ou expirou.", "sessionExpired": "A sessão expirou.", "failedToUnlinkLastAccount": "Falha ao desvincular conta", "accountNotFound": "Conta não encontrada"}, "forgotPassword": {"backToSignin": "Voltar para login", "email": "Email", "hints": {"linkNotSent": {"message": "<PERSON><PERSON><PERSON> muito, mas não foi possível enviar um link para redefinir sua senha. Tente novamente mais tarde.", "title": "Link não enviado"}, "linkSent": {"message": "Enviamos um link para continuar. Verifique sua caixa de entrada.", "title": "Link enviado"}}, "message": "Digite seu endereço de email e enviaremos um link para redefinir sua senha.", "submit": "Enviar link", "title": "Esqueceu sua senha?"}, "login": {"continueWith": "Ou continue com", "createAnAccount": "Criar uma conta", "dontHaveAnAccount": "Ainda não tem uma conta?", "forgotPassword": "Esque<PERSON>u a senha?", "hints": {"invalidCredentials": "O email ou senha que você inseriu são inválidos. Tente novamente.", "linkSent": {"message": "Enviamos um link para continuar. Verifique sua caixa de entrada.", "title": "Link enviado"}}, "loginWithPasskey": "Entrar com chave de acesso", "modes": {"magicLink": "<PERSON>", "password": "<PERSON><PERSON>"}, "submit": "Entrar", "subtitle": "Digite suas credenciais para entrar.", "title": "Bem-vindo de volta", "sendMagicLink": "Enviar link mágico"}, "resetPassword": {"backToSignin": "Voltar para login", "hints": {"error": "Sen<PERSON><PERSON> muito, mas não foi possível redefinir sua senha. Tente novamente.", "success": "Sua senha foi redefinida com sucesso."}, "message": "Digite uma nova senha.", "newPassword": "Nova senha", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Redefinir sua senha"}, "signup": {"alreadyHaveAccount": "Já tem uma conta?", "email": "Email", "hints": {"signupFailed": "<PERSON><PERSON><PERSON> muit<PERSON>, mas não foi possível criar sua conta. Tente novamente mais tarde.", "verifyEmail": "Enviamos um link para verificar seu email. Verifique sua caixa de entrada."}, "message": "Ficamos felizes que você queira se juntar a nós. Preencha o formulário abaixo para criar sua conta.", "name": "Nome", "password": "<PERSON><PERSON>", "signIn": "Entrar", "submit": "C<PERSON><PERSON> conta", "title": "Criar uma conta"}, "verify": {"title": "Verificar sua conta", "code": "Senha de uso único", "submit": "Verificar", "backToSignin": "Voltar para login", "message": "Digite a senha de uso único do seu aplicativo autenticador para continuar."}}, "blog": {"description": "<PERSON><PERSON> as últimas notícias da nossa empresa", "title": "Meu blog incrível", "back": "Voltar para o blog"}, "changelog": {"description": "Mantenha-se atualizado com as últimas mudanças em nosso produto.", "title": "Changelog"}, "common": {"confirmation": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar"}, "menu": {"blog": "Blog", "changelog": "Changelog", "contact": "Contato", "dashboard": "<PERSON><PERSON>", "docs": "Documentação", "faq": "FAQ", "login": "Entrar", "pricing": "Preços"}, "tableOfContents": {"title": "Nesta página"}, "actions": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "verify": "Verificar", "back": "Voltar"}}, "contact": {"description": "Estamos aqui para ajudá-lo. Use o formulário abaixo para entrar em contato conosco.", "form": {"email": "Email", "message": "Mensagem", "name": "Nome", "notifications": {"error": "<PERSON><PERSON><PERSON> muit<PERSON>, mas não foi possível enviar sua mensagem. Tente novamente mais tarde.", "success": "Sua mensagem foi enviada com sucesso. Entraremos em contato o mais breve possível."}, "submit": "Enviar mensagem"}, "title": "Entre em contato"}, "documentation": {"title": "Documentação"}, "faq": {"description": "Tem alguma dúvida? Nós temos você coberto.", "title": "<PERSON><PERSON><PERSON> frequentes"}, "mail": {"common": {"openLinkInBrowser": "Se você quiser abrir o link em um navegador diferente do seu padrão, copie e cole este link:", "otp": "Senha de uso único", "useLink": "ou use o seguinte link:", "greeting": "O<PERSON><PERSON>", "regards": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "team": "Equipe Cakto Members"}, "emailVerification": {"body": "<PERSON><PERSON><PERSON>,\nclique no link abaixo para verificar este novo endereço de email.", "confirmEmail": "Verificar email", "subject": "Verifique seu email"}, "forgotPassword": {"body": "<PERSON><PERSON><PERSON>,\nvocê solicitou uma redefinição de senha.\n\nClique no botão abaixo para redefinir sua senha.", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "subject": "Redefinir sua senha"}, "magicLink": {"body": "<PERSON><PERSON><PERSON>,\nvocê solicitou um email de login do Cakto Members.\n\nClique no link abaixo para fazer login.", "login": "Entrar", "subject": "Entrar no Cakto Members"}, "newUser": {"body": "<PERSON><PERSON><PERSON>,\nobrigado por se cadastrar no Cakto Members.\n\nPara começar a usar nosso aplicativo, confirme seu endereço de email clicando no link abaixo.", "confirmEmail": "Confirmar email", "subject": "Confirme seu email"}, "newsletterSignup": {"body": "Obrigado por se inscrever na newsletter do Cakto Members. Manteremos você atualizado com as últimas notícias e atualizações.", "subject": "Bem-vindo à nossa newsletter"}, "organizationInvitation": {"body": "Você foi convidado para se juntar à organização {organizationName}. Clique no botão abaixo ou copie e cole o link no seu navegador de preferência para aceitar o convite e se juntar à organização.", "headline": "Junte-se à organização {organizationName}", "join": "Juntar-se à organização", "subject": "Você foi convidado para se juntar a uma organização", "welcome": "Bem-vindo ao Cakto Members!", "invitation": "Convite para Organização", "invitedBy": "Você foi convidado por {inviterName} para se juntar à organização {organizationName}.", "benefits": "Como membro desta organização, você terá acesso a:", "benefitsList": ["Cursos exclusivos da organização", "Área de membros personalizada", "Suporte direto da equipe", "Comunidade de membros ativos"], "getStarted": "Para começar, clique no botão abaixo e crie sua conta ou faça login:", "needHelp": "Precisa de ajuda? Entre em contato conosco em", "supportEmail": "<EMAIL>"}, "memberInvitation": {"subject": "Convite para área de membros - {organizationName}", "headline": "Você foi convidado para a área de membros!", "welcome": "Bem-vindo ao Cakto Members!", "invitedBy": "Você foi convidado por {inviterName} para se juntar à área de membros da {organizationName}.", "body": "Estamos muito felizes em tê-lo conosco! A {organizationName} criou uma área de membros exclusiva onde você terá acesso a conteúdos, cursos e recursos especiais.", "whatYouGet": "O que você terá acesso:", "benefits": ["Cursos exclusivos e conteúdos premium", "Comunidade ativa de membros", "Suporte direto da equipe {organizationName}", "Atualizações e novidades em primeira mão"], "getStarted": "Para acessar sua área de membros, clique no botão abaixo:", "joinButton": "Acessar <PERSON>", "alternativeAccess": "Ou acesse diretamente pelo link:", "needHelp": "Precisa de ajuda?", "supportText": "Se você tiver alguma dúvida ou precisar de suporte, nossa equipe está aqui para ajudar.", "contactSupport": "Entrar em contato com suporte"}, "userCreated": {"subject": "Sua conta foi criada no Cakto Members", "headline": "Sua conta foi criada com sucesso!", "welcome": "Bem-vindo ao Cakto Members!", "body": "Sua conta foi criada com sucesso no Cakto Members. Agora você pode acessar nossa plataforma e começar a explorar todos os recursos disponíveis.", "credentials": "Suas credenciais de acesso:", "email": "Email: {email}", "password": "<PERSON>ha: {password}", "loginButton": "<PERSON><PERSON><PERSON>", "changePassword": "Recomendamos que você altere sua senha após o primeiro login.", "securityNote": "Por motivos de segurança, mantenha suas credenciais em local seguro e não as compartilhe com terceiros."}, "welcomeMagicLink": {"subject": "Bem-vindo ao Cakto Members! | Acesse sua conta agora", "headline": "Bem-vindo ao Cakto Members!", "welcome": "Sua conta foi criada com sucesso e você já tem acesso à nossa plataforma!", "product": "Produto: {productName}", "organization": "Organização: {organizationName}", "accessMessage": "Clique no botão abaixo para acessar sua conta e começar a usar a plataforma:", "accessButton": "Acessar <PERSON>", "securityNote": "Este link é válido por 24 horas e pode ser usado apenas uma vez.", "securityWarning": "Se você não solicitou este acesso, ignore este email.", "supportMessage": "Se tiver dúvidas, conte com nosso suporte em", "supportEmail": "<EMAIL>"}}, "newsletter": {"email": "Email", "hints": {"error": {"message": "Não foi possível se inscrever na newsletter. Tente novamente mais tarde."}, "success": {"message": "Obrigado por se inscrever na nossa newsletter. Manteremos você informado.", "title": "Inscrito"}}, "submit": "Inscrever", "subtitle": "Seja um dos primeiros a ter acesso ao Cakto Members.", "title": "Obter acesso antecipado"}, "onboarding": {"account": {"avatar": "Avatar", "avatarDescription": "Clique no círculo ou arraste uma imagem para ele para fazer upload do seu avatar.", "name": "Nome"}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "message": "Apenas alguns passos rápidos para começar.", "notifications": {"accountSetupFailed": "<PERSON><PERSON><PERSON> muit<PERSON>, mas não foi possível configurar sua conta. Tente novamente mais tarde."}, "step": "Etapa {step} / {total}", "title": "Configure sua conta"}, "organizations": {"createForm": {"name": "Nome do workspace", "description": "Descrição", "descriptionPlaceholder": "Descreva o propósito deste workspace", "notifications": {"error": "Não foi possível criar seu workspace. Por favor, tente novamente mais tarde.", "success": "Seu workspace foi criado. Agora você pode convidar membros."}, "submit": "Criar workspace", "subtitle": "Vamos configurar seu novo workspace para começar.", "title": "Criar um workspace", "role": "Função", "sidebar": {"title": "<PERSON><PERSON>", "tip1": "Escolha um nome claro e memorável para seu workspace", "tip2": "A descrição ajuda outros a entenderem o propósito do workspace", "tip3": "Você pode personalizar tudo depois nas configurações"}, "infoStep": {"title": "Informações básicas", "description": "Forneça informações básicas sobre o seu workspace.", "nameDescription": "Este será o nome exibido para você e seus membros", "descriptionDescription": "Uma breve descrição do propósito do seu workspace (opcional)", "previewTitle": "Preview do workspace", "tipsTitle": "Dicas para um bom workspace", "tip1Title": "Nome claro", "tip1Description": "Use um nome que seja fácil de lembrar e identificar", "tip2Title": "Descrição útil", "tip2Description": "Explique brevemente o que o workspace oferece", "tip3Title": "Organização", "tip3Description": "Mantenha tudo organizado desde o início"}, "previewStep": {"title": "Personalizar workspace", "description": "Adicione um logo e veja como ficará seu workspace.", "logoTitle": "Logo do workspace", "logoDescription": "Adicione um logo personalizado para seu workspace (opcional)", "previewTitle": "Como ficará seu workspace", "placeholderName": "Seu workspace", "readyStatus": "Pronto para criar", "nextStepsTitle": "O que você poderá fazer", "nextStep1": "Convide membros da sua equipe", "nextStep2": "Crie cursos e conteúdo", "nextStep3": "Configure preferências a<PERSON>das"}, "completionStep": {"title": "Workspace criado com sucesso!", "description": "Seu workspace '{name}' está pronto para uso.", "summaryTitle": "Resumo da criação", "role": "Sua função", "nextStepsTitle": "Próximos passos", "nextStep1": "Convide membros para seu workspace", "nextStep2": "Configure as preferências do seu workspace", "nextStep3": "Comece a criar cursos e conteúdo", "readyTitle": "Tudo pronto!", "readyDescription": "Seu workspace foi criado e está pronto para uso. Clique em 'Criar workspace' para finalizar.", "readyStatus": "Workspace pronto para uso"}}, "invitationAlert": {"description": "Você precisa fazer login ou criar uma conta para se juntar ao workspace.", "title": "Você foi convidado para se juntar a um workspace."}, "invitationModal": {"accept": "Aceitar", "decline": "Recusar", "description": "Você foi convidado para se juntar ao workspace {organizationName}. Deseja aceitar o convite e se juntar ao workspace?", "title": "Juntar-se ao workspace"}, "organizationSelect": {"createNewOrganization": "Criar novo workspace", "organizations": "Workspaces", "personalAccount": "<PERSON>ta pessoal"}, "organizationsGrid": {"createNewOrganization": "Criar novo workspace", "title": "Seus workspaces"}, "roles": {"admin": "Administrador", "member": "Membro", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"changeName": {"title": "Nome do workspace"}, "deleteOrganization": {"confirmation": "Tem certeza de que deseja excluir seu workspace?", "description": "Excluir permanentemente seu workspace. Uma vez que você excluir seu workspace, não há como voltar atrás. Para confirmar, digite sua senha abaixo:", "submit": "Excluir workspace", "title": "Excluir workspace"}, "logo": {"description": "Faça upload de um logo para seu workspace.", "title": "Logo do workspace"}, "memberArea": {"title": "Configurações da Área de Membros", "general": {"title": "Configurações Gerais", "description": "Configure as configurações básicas da sua área de membros"}, "appearance": {"title": "Aparência", "description": "Personalize a aparência visual da sua área de membros"}, "features": {"title": "Recursos", "description": "Ative ou desative recursos da área de membros"}, "domain": {"title": "Configurações de Domínio", "description": "Configure o domínio personalizado para sua área de membros", "subdomain": {"title": "Subdomínio", "description": "Seu subdomínio personalizado para acessar a área de membros", "placeholder": "minha-area", "suffix": ".aluno.cakto.com.br"}, "customDomain": {"title": "<PERSON><PERSON><PERSON>", "description": "Use seu próprio domínio para a área de membros", "placeholder": "alunos.minhaempresa.com.br", "instructions": {"title": "Instruções de Configuração DNS", "step1": "Adicione um registro CNAME no seu provedor de DNS:", "step2": "Nome/Host: <PERSON><PERSON><PERSON> (ou o subdomínio desejado)", "step3": "Valor/Destino: aluno.cakto.com.br", "step4": "Aguarde até 24 horas para a propagação do DNS", "note": "Nota: A propagação do DNS pode levar até 24 horas. Entre em contato com nosso suporte se precisar de ajuda."}}}, "menu": {"title": "Menu de Navegação", "description": "Configure o menu de navegação da sua área de membros", "addItem": "Adicionar Item do <PERSON>u", "titlePlaceholder": "Título do item do menu", "urlPlaceholder": "/caminho", "iconPlaceholder": "Selecionar ícone", "position": "Posição"}, "memberAreaName": "Nome da Área de Me<PERSON>ros", "subdomain": "Subdomínio", "supportEmail": "Email de Suporte", "primaryColor": "Cor Principal", "logoUrl": "URL do Logo", "commentsEnabled": "Ativar Comentários", "save": "<PERSON>var Configura<PERSON>", "notifications": {"success": "Configurações da área de membros atualizadas com sucesso", "error": "<PERSON><PERSON><PERSON> ao at<PERSON><PERSON>r as configurações da área de membros"}}, "members": {"activeMembers": "Membros ativos", "description": "Veja todos os membros ativos e os convites pendentes do seu workspace.", "invitations": {"empty": "Você ainda não convidou nenhum membro.", "expiresAt": "Expira em {date}", "invitationStatus": {"accepted": "<PERSON><PERSON>", "canceled": "Cancelado", "pending": "Pendente", "rejected": "<PERSON><PERSON><PERSON><PERSON>"}, "revoke": "<PERSON><PERSON><PERSON> convite", "resend": "Reenviar convite"}, "inviteMember": {"description": "Para convidar um novo membro, envie um convite.", "email": "Email", "notifications": {"error": {"description": "Não foi possível convidar o membro. Tente novamente mais tarde.", "title": "Não foi possível convidar membro"}, "success": {"description": "O membro foi convidado.", "title": "Membro convidado"}}, "role": "Função", "submit": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> membro"}, "leaveOrganization": "Sair do workspace", "notifications": {"removeMember": {"error": {"description": "Não foi possível remover o membro do seu workspace. Tente novamente."}, "loading": {"description": "Removendo membro do workspace..."}, "success": {"description": "O membro foi removido com sucesso do seu workspace."}}, "revokeInvitation": {"error": {"description": "O convite não pôde ser revogado. Tente novamente mais tarde."}, "loading": {"description": "Revogando convite..."}, "success": {"description": "O convite foi revogado."}}, "updateMembership": {"error": {"description": "Não foi possível atualizar a associação do workspace. Tente novamente."}, "loading": {"description": "Atualizando associação..."}, "success": {"description": "Associação foi atualizada com sucesso"}}, "resendInvitation": {"loading": {"description": "<PERSON><PERSON><PERSON><PERSON> convite..."}, "success": {"description": "Convite enviado"}, "error": {"description": "Não foi possível enviar convite. Tente novamente."}}}, "pendingInvitations": "Convites pendentes", "removeMember": "Remover membro", "title": "Me<PERSON><PERSON>"}, "notifications": {"organizationDeleted": "Seu workspace foi excluído.", "organizationNameNotUpdated": "Não foi possível atualizar o nome do seu workspace. Tente novamente mais tarde.", "organizationNameUpdated": "O nome do seu workspace foi atualizado.", "organizationNotDeleted": "Não foi possível excluir seu workspace. Tente novamente mais tarde."}, "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configuraç<PERSON><PERSON> do workspace.", "title": "Workspace", "dangerZone": {"title": "Zona de perigo"}}, "start": {"subtitle": "Bem-vindo à página inicial deste workspace!"}}, "pricing": {"choosePlan": "Escolher plano", "contactSales": "<PERSON><PERSON><PERSON> vendas", "description": "Escolha o plano que funciona melhor para você.", "getStarted": "<PERSON><PERSON><PERSON>", "month": "{count, plural, one {mês} other {{count} meses}}", "monthly": "Mensal", "products": {"basic": {"description": "Perfeito para pequenas equipes.", "features": {"anotherFeature": "Outra funcionalidade incrível", "limitedSupport": "Suporte limitado"}, "title": "Básico"}, "enterprise": {"description": "Plano personalizado adaptado às suas necessidades", "features": {"enterpriseSupport": "Suporte empresarial", "unlimitedProjects": "Projetos ilimitados"}, "title": "Empresarial"}, "free": {"description": "Comece gratuitamente", "features": {"anotherFeature": "Outra funcionalidade incrível", "limitedSupport": "Suporte limitado"}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "lifetime": {"description": "Compre uma vez. Use para sempre.", "features": {"extendSupport": "Suporte estendido", "noRecurringCosts": "Sem custos recorrentes"}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "pro": {"description": "Melhor para equipes", "features": {"anotherFeature": "Outra funcionalidade incrível", "fiveMembers": "Até 5 membros", "fullSupport": "Suporte completo"}, "title": "Pro"}}, "purchase": "<PERSON><PERSON><PERSON>", "recommended": "Recomendado", "subscribe": "<PERSON><PERSON><PERSON>", "title": "Preços", "trialPeriod": "{days} {days, plural, one {dia} other {dias}} de teste gratuito", "year": "{count, plural, one {ano} other {{count} anos}}", "yearly": "<PERSON><PERSON>", "perSeat": "assento"}, "settings": {"account": {"avatar": {"description": "Para alterar seu avatar, clique na imagem neste bloco e selecione um arquivo do seu computador para fazer upload.", "notifications": {"error": "Não foi possível atualizar avatar", "success": "Avatar foi atualizado com sucesso"}, "title": "Seu avatar"}, "changeEmail": {"description": "Para alterar seu email, digite o novo email e clique em salvar. Você terá que confirmar o novo email antes que ele se torne ativo.", "notifications": {"error": "Não foi possível atualizar email", "success": "Email foi atualizado com sucesso"}, "title": "Seu email"}, "changeName": {"notifications": {"error": "Não foi possível atualizar nome", "success": "Nome foi atualizado com sucesso"}, "title": "Seu nome"}, "deleteAccount": {"confirmation": "Tem certeza de que deseja excluir sua conta?", "description": "Excluir permanentemente sua conta. Uma vez que você excluir sua conta, não há como voltar atrás. Para confirmar, digite sua senha abaixo:", "notifications": {"error": "Não foi possível excluir conta", "success": "Conta foi excluída com sucesso"}, "submit": "Excluir conta", "title": "Excluir conta"}, "language": {"description": "Para alterar o idioma do aplicativo para sua conta, selecione um idioma da lista e clique em salvar.", "notifications": {"error": "Não foi possível atualizar idioma", "success": "Idioma foi atualizado com sucesso"}, "title": "Seu idioma"}, "security": {"activeSessions": {"description": "<PERSON><PERSON><PERSON> s<PERSON> as sessões ativas da sua conta. Clique no X para encerrar uma sessão específica.", "title": "Sessõ<PERSON> at<PERSON>", "notifications": {"revokeSession": {"success": "Sessão revogada"}}, "currentSession": "<PERSON>ss<PERSON> atual"}, "changePassword": {"currentPassword": "<PERSON><PERSON> atual", "newPassword": "Nova senha", "notifications": {"error": "Não foi possível atualizar senha", "success": "Senha foi atualizada com sucesso"}, "title": "<PERSON><PERSON> se<PERSON>a"}, "connectedAccounts": {"connect": "Conectar", "disconnect": "Desconectar", "title": "Contas conectadas"}, "passkeys": {"description": "Use chaves de acesso como uma alternativa segura às senhas.", "notifications": {"addPasskey": {"error": {"title": "Não foi possível adicionar chave de acesso"}, "success": {"title": "Chave de acesso adicionada"}}, "deletePasskey": {"error": {"title": "Não foi possível excluir chave de acesso"}, "loading": {"title": "Excluindo chave de acesso..."}, "success": {"title": "Chave de acesso excluída"}}}, "title": "<PERSON><PERSON>"}, "setPassword": {"description": "Você ainda não definiu uma senha. Para definir uma, você precisa passar pelo fluxo de redefinição de senha. Clique no botão abaixo para enviar um email para redefinir sua senha e siga as instruções no email.", "notifications": {"error": "Não foi possível enviar link para definir senha. Tente novamente.", "success": "Verifique sua caixa de entrada para o link para definir sua senha."}, "submit": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON> se<PERSON>a"}, "title": "Segurança", "twoFactor": {"title": "Autenticação de dois fatores", "description": "Adicione uma camada extra de segurança à sua conta.", "dialog": {"password": {"title": "Verificar com senha", "description": "Por favor, verifique sua conta digitando sua senha:", "label": "<PERSON><PERSON> senha:"}, "totpUrl": {"description": "Use seu aplicativo autenticador preferido e escaneie o código QR com ele ou digite o segredo abaixo manualmente para configurar a autenticação de dois fatores.", "code": "Digite o código de 6 dígitos para verificar a configuração:", "title": "Ativar autenticação de dois fatores"}}, "enable": "Ativar autenticação de dois fatores", "notifications": {"verify": {"success": {"title": "A autenticação de dois fatores foi ativada com sucesso."}}, "enable": {"error": {"title": "Não foi possível verificar sua conta com a senha fornecida. Tente novamente."}}, "disable": {"success": {"title": "A autenticação de dois fatores foi desativada com sucesso."}}}, "disable": "Desativar autenticação de dois fatores", "enabled": "Você tem autenticação de dois fatores ativada para sua conta."}}, "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configura<PERSON><PERSON><PERSON> da sua conta pessoal.", "title": "Configurações da conta"}, "billing": {"createCustomerPortal": {"label": "Gerenciar cobrança", "notifications": {"error": {"title": "Não foi possível criar uma sessão do portal do cliente. Tente novamente."}}}, "activePlan": {"status": {"active": "Ativo", "canceled": "Cancelado", "expired": "<PERSON><PERSON><PERSON>", "incomplete": "Incompleto", "past_due": "V<PERSON>cid<PERSON>", "paused": "<PERSON><PERSON><PERSON>", "trialing": "Em teste", "unpaid": "Não pago"}, "title": "Seu plano"}, "changePlan": {"description": "Escolha um plano para assinar.", "title": "Alterar seu plano"}, "title": "Cobrança"}, "menu": {"account": {"billing": "Cobrança", "customize": "Customizar", "dangerZone": "Zona de perigo", "general": "G<PERSON>", "security": "Segurança", "title": "Conta"}, "organization": {"billing": "Cobrança", "general": "G<PERSON>", "members": "Me<PERSON><PERSON>", "memberArea": "<PERSON><PERSON>", "title": "Workspace", "dangerZone": "Zona de perigo"}}, "customize": {"title": "<PERSON><PERSON><PERSON>", "notifications": {"success": "Configurações salvas com sucesso!", "error": "Erro ao salvar configurações"}}, "save": "<PERSON><PERSON>"}, "start": {"subtitle": "<PERSON><PERSON><PERSON> as últimas estatísticas do seu negócio incrível.", "welcome": "<PERSON><PERSON>-vindo {name}!"}, "zod": {"errors": {"invalid_arguments": "Argumentos de função inválidos", "invalid_date": "Data inválida", "invalid_enum_value": "<PERSON>or de enum inválido. Esperado {- options}, recebido '{received}'", "invalid_intersection_types": "Os resultados da interseção não puderam ser mesclados", "invalid_literal": "Valor literal inválido, esperado {expected}", "invalid_return_type": "Tipo de retorno de função inválido", "invalid_string": {"cuid": "{validation} inv<PERSON><PERSON>o", "datetime": "{validation} inv<PERSON><PERSON>o", "email": "{validation} inv<PERSON><PERSON>o", "endsWith": "Entrada inválida: deve terminar com \"{endsWith}\"", "regex": "<PERSON>v<PERSON><PERSON><PERSON>", "startsWith": "Entrada inválida: deve come<PERSON>r com \"{startsWith}\"", "url": "{validation} inv<PERSON><PERSON>o", "uuid": "{validation} inv<PERSON><PERSON>o"}, "invalid_type": "E<PERSON><PERSON> {expected}, recebido {received}", "invalid_type_received_undefined": "Obrigatório", "invalid_union": "Entrada inválida", "invalid_union_discriminator": "Valor discriminador inválido. Esperado {- options}", "not_finite": "Número deve ser finito", "not_multiple_of": "Número deve ser um múltiplo de {multipleOf}", "too_big": {"array": {"exact": "Array deve conter exatamente {maximum} elemento(s)", "inclusive": "Array deve conter no máximo {maximum} elemento(s)", "not_inclusive": "Array deve conter menos de {maximum} elemento(s)"}, "date": {"exact": "Data deve ser exatamente {- maximum, datetime}", "inclusive": "Data deve ser menor ou igual a {- maximum, datetime}", "not_inclusive": "Data deve ser menor que {- maximum, datetime}"}, "number": {"exact": "Número deve ser exatamente {maximum}", "inclusive": "Número deve ser menor ou igual a {maximum}", "not_inclusive": "Número deve ser menor que {maximum}"}, "set": {"exact": "Entrada inválida", "inclusive": "Entrada inválida", "not_inclusive": "Entrada inválida"}, "string": {"exact": "String deve conter exatamente {maximum} caractere(s)", "inclusive": "String deve conter no máximo {maximum} caractere(s)", "not_inclusive": "String deve conter menos de {maximum} caractere(s)"}}, "too_small": {"array": {"exact": "Array deve conter exatamente {minimum} elemento(s)", "inclusive": "Array deve conter pelo menos {minimum} elemento(s)", "not_inclusive": "Array deve conter mais de {minimum} elemento(s)"}, "date": {"exact": "Data deve ser exatamente {- minimum, datetime}", "inclusive": "Data deve ser maior ou igual a {- minimum, datetime}", "not_inclusive": "Data deve ser maior que {- minimum, datetime}"}, "number": {"exact": "Número deve ser exatamente {minimum}", "inclusive": "Número deve ser maior ou igual a {minimum}", "not_inclusive": "Número deve ser maior que {minimum}"}, "set": {"exact": "Entrada inválida", "inclusive": "Entrada inválida", "not_inclusive": "Entrada inválida"}, "string": {"exact": "String deve conter exatamente {minimum} caractere(s)", "inclusive": "String deve conter pelo menos {minimum} caractere(s)", "not_inclusive": "String deve conter mais de {minimum} caractere(s)"}}, "unrecognized_keys": "Chave(s) não reconhecida(s) no objeto: {- keys}"}, "types": {"array": "array", "bigint": "bigint", "boolean": "boolean", "date": "data", "float": "float", "function": "função", "integer": "inteiro", "map": "mapa", "nan": "nan", "never": "never", "null": "null", "number": "número", "object": "objeto", "promise": "promessa", "set": "conjunto", "string": "string", "symbol": "sí<PERSON>lo", "undefined": "indefinido", "unknown": "desconhecido", "void": "void"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "datetime", "email": "email", "emoji": "emoji", "ip": "ip", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}, "choosePlan": {"title": "Escolha seu plano", "description": "Para continuar, selecione um plano."}}