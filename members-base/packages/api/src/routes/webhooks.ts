import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { z } from "zod";
import { createUser, createUserAccount, getUserByEmail, db } from "@repo/database";

import { logger } from "@repo/logs";
import crypto from "crypto";

export const webhooksRouter = new Hono().post(
	"/webhooks/cakto/purchase",
	describeRoute({
		tags: ["Webhooks"],
		summary: "Handle Cakto purchase webhook",
		description: "Process approved purchases from Cakto platform and grant course access",
	}),
	async (c) => {
		try {
			// Verificar se há chave secreta para autenticação HMAC
			const signature = c.req.header("X-Cakto-Signature");
			const expectedSecret = process.env.MEMBERS_WEBHOOK_SECRET;

			if (!expectedSecret) {
				logger.error("MEMBERS_WEBHOOK_SECRET not configured");
				return c.json({ success: false, message: "Webhook not configured" }, 500);
			}

			// Obter o corpo da requisição como string para validação HMAC
			const body = await c.req.text();
			const payload = JSON.parse(body);

			// Validar assinatura HMAC (igual ao cakto-backend)
			if (signature) {
				const expectedSignature = crypto
					.createHmac('sha256', expectedSecret)
					.update(body)
					.digest('hex');

				if (signature !== expectedSignature) {
					logger.error("Invalid HMAC signature");
					return c.json({ success: false, message: "Invalid signature" }, 401);
				}
			}

			// Schema para validação do payload real da Cakto
			const caktoWebhookSchema = z.object({
				secret: z.string(),
				event: z.string(),
				data: z.object({
					id: z.string(),
					refId: z.string(),
					customer: z.object({
						name: z.string(),
						email: z.string().email(),
						phone: z.string().optional(),
						docNumber: z.string().optional(),
					}),
					affiliate: z.string().optional(),
					offer: z.object({
						id: z.string(),
						name: z.string(),
						price: z.number(),
					}).optional(),
					offer_type: z.string().optional(),
					product: z.object({
						name: z.string(),
						id: z.string(),
						short_id: z.string().optional(),
						supportEmail: z.string().optional(),
						type: z.string().optional(),
						invoiceDescription: z.string().optional(),
					}),
					parent_order: z.string().optional(),
					checkoutUrl: z.string().optional(),
					status: z.string(),
					baseAmount: z.number().optional(),
					discount: z.number().optional(),
					amount: z.number().optional(),
					commissions: z.array(z.object({
						user: z.string(),
						totalAmount: z.number(),
						type: z.string(),
						percentage: z.number(),
					})).optional(),
					reason: z.string().optional(),
					refund_reason: z.string().optional(),
					installments: z.number().optional(),
					paymentMethod: z.string().optional(),
					paymentMethodName: z.string().optional(),
					paidAt: z.string().optional().nullable(),
					createdAt: z.string(),
					card: z.object({
						lastDigits: z.string(),
						holderName: z.string(),
						brand: z.string(),
					}).optional(),
					boleto: z.object({
						barcode: z.string(),
						boletoUrl: z.string(),
						expirationDate: z.string(),
					}).optional(),
					pix: z.object({
						expirationDate: z.string(),
						qrCode: z.string(),
					}).optional(),
					picpay: z.object({
						qrCode: z.string(),
						paymentURL: z.string(),
						expirationDate: z.string(),
					}).optional(),
				}),
			});

			const validationResult = caktoWebhookSchema.safeParse(payload);
			if (!validationResult.success) {
				logger.error("Invalid webhook payload", validationResult.error);
				return c.json({ success: false, message: "Invalid payload" }, 400);
			}

			const { data, event } = validationResult.data;
			const { customer, product, status } = data;

			if (event !== "purchase_approved") {
				return c.json({
					success: true,
					message: `Webhook received but event is not purchase_approved (${event})`
				}, 200);
			}

			// Verificar se o status é de aprovação
			if (status !== "approved" && status !== "completed") {
				return c.json({
					success: true,
					message: `Webhook received but status is not approved (${status})`
				}, 200);
			}

			// Verificar se o usuário já existe
			let user = await getUserByEmail(customer.email);

			// Se não existir, criar o usuário
			if (!user) {
				logger.info(`Creating new user for ${customer.email}`);

				// Criar o usuário sem senha (usará magic link)
				user = await createUser({
					email: customer.email,
					name: customer.name,
					role: "user",
					emailVerified: true,
					onboardingComplete: false,
				});

				if (!user) {
					throw new Error(`Failed to create user for ${customer.email}`);
				}

				logger.info(`User created successfully for ${customer.email}`);
			}

			// Buscar ou criar organização padrão
			let defaultOrganization = await db.organization.findFirst({
				where: { name: "Cakto Members" }
			});

			if (!defaultOrganization) {
				defaultOrganization = await db.organization.create({
					data: {
						name: "Cakto Members",
						slug: "cakto-members",
						createdAt: new Date(),
					}
				});
				logger.info(`Created default organization: ${defaultOrganization.name}`);
			}

			// Verificar se o usuário já é membro da organização
			const existingMember = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: defaultOrganization.id,
				},
			});

			// Se não for membro, adicionar à organização
			if (!existingMember) {
				await db.member.create({
					data: {
						userId: user.id,
						organizationId: defaultOrganization.id,
						role: "member",
						createdAt: new Date(),
					},
				});
				logger.info(`User ${user.email} added to organization ${defaultOrganization.name}`);
			}

			// Buscar o curso relacionado ao produto
			const courseProduct = await db.courseProduct.findFirst({
				where: {
					caktoProductId: product.id,
				},
				include: {
					course: true,
				},
			});

			if (!courseProduct) {
				logger.error(`No course found for product ID: ${product.id}`);
				return c.json({
					success: false,
					message: `No course found for product ID: ${product.id}`
				}, 404);
			}

			// Verificar se o usuário já tem acesso ao curso
			const existingAccess = await db.userCourses.findFirst({
				where: {
					userId: user.id,
					courseId: courseProduct.courseId,
				},
			});

			if (existingAccess) {
				logger.info(`User ${user.email} already has access to course ${courseProduct.course.name}`);
				return c.json({
					success: true,
					message: "User already has access to this course"
				}, 200);
			}

			// Conceder acesso ao curso
			await db.userCourses.create({
				data: {
					userId: user.id,
					courseId: courseProduct.courseId,
					finalTime: null, // Sem data de expiração
				},
			});

			logger.info(`Access granted: User ${user.email} to course ${courseProduct.course.name}`);

			// Enviar magic link para o usuário
			const auth = await import("@repo/auth").then(m => m.auth);
			const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://members.cakto.com.br";

			// Gerar magic link usando Better Auth
			try {
				await auth.api.signInMagicLink({
					body: {
						email: user.email,
						callbackURL: `${baseUrl}/app/courses/${courseProduct.courseId}`,
					},
					headers: new Headers(),
				});
				logger.info(`Magic link sent successfully to ${user.email}`);
			} catch (error) {
				logger.error(`Failed to send magic link to ${user.email}:`, error);
			}

			return c.json({
				success: true,
				message: "Purchase processed successfully",
				data: {
					userId: user.id,
					courseId: courseProduct.courseId,
					courseName: courseProduct.course.name,
				},
			}, 200);
		} catch (error) {
			logger.error("Error processing Cakto webhook", error);
			return c.json({
				success: false,
				message: `Error processing webhook: ${error instanceof Error ? error.message : "Unknown error"}`
			}, 500);
		}
	},
);
