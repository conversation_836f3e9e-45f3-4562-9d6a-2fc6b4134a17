import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
	userId: z.string(),
});

export const getOrganizationStats = new Hono()
	.use(authMiddleware)
	.get("/stats", validator("query", querySchema), async (c) => {
		try {
			const { userId } = c.req.valid("query");

			// Get user's organizations with comprehensive stats
			const organizations = await db.organization.findMany({
				where: {
					members: {
						some: {
							userId: userId,
						},
					},
				},
				include: {
					members: {
						select: {
							id: true,
							role: true,
							createdAt: true,
						},
					},
					courses: {
						select: {
							id: true,
							name: true,
							createdAt: true,
							userCourses: {
								select: {
									userId: true,
								},
							},
						},
					},
					vitrines: {
						select: {
							id: true,
							title: true,
							status: true,
							createdAt: true,
							views: {
								select: {
									id: true,
									createdAt: true,
								},
							},
						},
					},
					_count: {
						select: {
							members: true,
							courses: true,
							vitrines: true,
						},
					},
				},
				orderBy: {
					name: "asc",
				},
			});


			const organizationsWithStats = organizations.map((org) => {

				const thirtyDaysAgo = new Date();
				thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

				const activeMembers = org.members.filter(
					(member) => member.createdAt >= thirtyDaysAgo
				).length;

				const totalEnrollments = org.courses.reduce(
					(acc, course) => acc + course.userCourses.length,
					0
				);

				const totalViews = org.vitrines.reduce(
					(acc, vitrine) => acc + vitrine.views.length,
					0
				);

				const publishedVitrines = org.vitrines.filter(
					(vitrine) => vitrine.status === "PUBLISHED"
				).length;

				const lastCourseActivity = org.courses.length > 0
					? Math.max(...org.courses.map(c => c.createdAt.getTime()))
					: 0;

				const lastVitrineActivity = org.vitrines.length > 0
					? Math.max(...org.vitrines.map(v => v.createdAt.getTime()))
					: 0;

				const lastActivity = Math.max(lastCourseActivity, lastVitrineActivity);

				const daysSinceLastActivity = lastActivity > 0
					? Math.floor((Date.now() - lastActivity) / (1000 * 60 * 60 * 24))
					: null;

				return {
					id: org.id,
					name: org.name,
					slug: org.slug,
					logo: org.logo,
					createdAt: org.createdAt.toISOString(),
					stats: {
						totalMembers: org._count.members,
						activeMembers,
						totalCourses: org._count.courses,
						totalEnrollments,
						totalVitrines: org._count.vitrines,
						publishedVitrines,
						totalViews,
						daysSinceLastActivity,
					},
				};
			});

			return c.json(organizationsWithStats);
		} catch (error) {
			console.error("Error fetching organization stats:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
