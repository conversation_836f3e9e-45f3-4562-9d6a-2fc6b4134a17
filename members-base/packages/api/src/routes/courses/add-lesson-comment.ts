import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

const paramsSchema = z.object({
  lessonId: z.string(),
})

const bodySchema = z.object({
  content: z.string().min(1).max(1000),
})

export const addLessonComment = new Hono()
  .post('/lesson/:lessonId/comments', validator('param', paramsSchema), validator('json', bodySchema), async (c) => {
    try {
      const { lessonId } = c.req.valid('param')
      const { content } = c.req.valid('json')

      // Implementação da inserção no banco de dados será adicionada posteriormente
      const newComment = {
        id: Date.now().toString(),
        content,
        lessonId,
        userId: 'current-user-id', // Obter do contexto de autenticação
        createdAt: new Date().toISOString(),
        user: {
          id: 'current-user-id',
          name: 'Current User',
          email: '<EMAIL>',
        },
      }

      return c.json({ comment: newComment })
    } catch (error) {
      return c.json({ error: 'Failed to add lesson comment' }, 500)
    }
  })
