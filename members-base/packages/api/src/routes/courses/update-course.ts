import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	courseId: z.string(),
});

const updateCourseSchema = z.object({
	name: z.string().min(1, "Course name is required").optional(),
	description: z.string().optional(),
	organizationId: z.string().optional(),
	community: z.string().optional(),
	link: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
		message: "Link must be a valid URL if provided",
	}),
	logo: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
		message: "Logo must be a valid URL if provided",
	}),
	modules: z.array(z.object({
		name: z.string(),
		position: z.number(),
		cover: z.string().optional(),
	})).optional(),
});

export const updateCourse = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.put("/:courseId", validator("param", paramsSchema), async (c) => {
		try {
			const user = c.get("user");
			const { courseId } = c.req.valid("param");

			// Parse and validate the request body
			let data;
			try {
				const rawData = await c.req.json();
				data = updateCourseSchema.parse(rawData);
			} catch (error) {
				console.error("Validation error:", error);
				if (error instanceof z.ZodError) {
					return c.json({
						error: "Validation failed",
						details: error.errors.map(e => ({
							field: e.path.join('.'),
							message: e.message
						}))
					}, 400);
				}
				return c.json({ error: "Invalid request data" }, 400);
			}

			console.log('🔍 Admin Update Course API - Starting course update:', { courseId, userId: user.id, userRole: user.role });

			// Check if course exists
			const existingCourse = await db.courses.findUnique({
				where: { id: courseId },
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
			});

			if (!existingCourse) {
				return c.json({ error: "Course not found" }, 404);
			}

			// Update course basic information
			const updatedCourse = await db.courses.update({
				where: { id: courseId },
				data: {
					...(data.name && { name: data.name }),
					...(data.description !== undefined && { description: data.description }),
					...(data.community !== undefined && { community: data.community }),
					...(data.link !== undefined && { link: data.link }),
					...(data.logo !== undefined && { logo: data.logo }),
					...(data.organizationId && { organizationId: data.organizationId }),
					updatedAt: new Date(),
				},
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
			});

			// Handle modules update if provided
			if (data.modules) {
				// For now, we'll just log the modules update
				// In a full implementation, you'd want to handle module creation/update/deletion
				console.log('🔍 Admin Update Course API - Modules update requested:', data.modules);
				// Implementação das atualizações de módulos será adicionada posteriormente
			}

			console.log('✅ Admin Update Course API - Course updated successfully:', { courseId, name: updatedCourse.name });

			return c.json({
				id: updatedCourse.id,
				name: updatedCourse.name,
				description: updatedCourse.description,
				community: updatedCourse.community,
				link: updatedCourse.link,
				logo: updatedCourse.logo,
				organizationId: updatedCourse.organizationId,
				createdAt: updatedCourse.createdAt.toISOString(),
				updatedAt: updatedCourse.updatedAt.toISOString(),
				organization: updatedCourse.organization,
			});
		} catch (error) {
			console.error("❌ Admin Update Course API - Error updating course:", error);
			return c.json({ error: "Failed to update course" }, 500);
		}
	});
