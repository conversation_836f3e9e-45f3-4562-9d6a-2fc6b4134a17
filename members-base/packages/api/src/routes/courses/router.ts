import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";

const coursesRouter = new Hono();

coursesRouter.use("*", authMiddleware);

coursesRouter.get("/test", (c) => {
	return c.json({ message: "Course API is working!" });
});

coursesRouter.get("/test/simple", (c) => {
	return c.json({ message: "Simple test route working!" });
});

coursesRouter.get("/test/auth", authMiddleware, (c) => {
	return c.json({ message: "Auth test route working!", user: c.get("user") });
});

coursesRouter.get("/:courseId", async (c) => {
	try {
		const courseId = c.req.param("courseId");
		const organizationSlug = c.req.query("organizationSlug");
		const user = c.get("user");

		if (!courseId || !organizationSlug) {
			return c.json({ error: "Course ID and organization slug are required" }, 400);
		}

		const organization = await db.organization.findFirst({
			where: { slug: organizationSlug },
		});

		if (!organization) {
			return c.json({ error: "Organization not found" }, 404);
		}

		if (user.role === "admin") {
			const course = await db.courses.findFirst({
				where: { id: courseId },
				include: {
					modules: {
						include: {
							lessons: {
								orderBy: { order: "asc" },
							},
						},
						orderBy: { order: "asc" },
					},
				},
			});

			if (!course) {
				return c.json({ error: "Course not found" }, 404);
			}

			return c.json({ course });
		}

		const userMembership = await db.member.findFirst({
			where: {
				userId: user.id,
				organizationId: organization.id,
			},
		});

		if (!userMembership) {
			return c.json({ error: "User is not a member of this organization" }, 403);
		}

		const course = await db.courses.findFirst({
			where: {
				id: courseId,
				organizationId: organization.id,
			},
			include: {
				modules: {
					include: {
						lessons: {
							orderBy: { order: "asc" },
						},
					},
					orderBy: { order: "asc" },
				},
			},
		});

		if (!course) {
			return c.json({ error: "Course not found" }, 404);
		}

		const userCourseAccess = await db.userCourses.findFirst({
			where: {
				userId: user.id,
				courseId: course.id,
			},
		});

		if (!userCourseAccess && course.createdBy !== user.id) {
			return c.json({ error: "User does not have access to this course" }, 403);
		}

		const formattedCourse = {
			id: course.id,
			name: course.name,
			description: course.description,
			image: course.image,
			createdAt: course.createdAt,
			updatedAt: course.updatedAt,
			modules: course.modules.map((module) => ({
				id: module.id,
				name: module.name,
				description: module.description,
				order: module.order,
				lessons: module.lessons.map((lesson) => ({
					id: lesson.id,
					name: lesson.name,
					description: lesson.description,
					videoUrl: lesson.videoUrl,
					order: lesson.order,
					duration: lesson.duration,
				})),
			})),
		};

		return c.json({ course: formattedCourse });
	} catch (error) {
		logger.error("Error fetching course:", error);
		return c.json({ error: "Internal server error" }, 500);
	}
});

coursesRouter.all("*", (c) => {
	return c.json({ error: "Route not found" }, 404);
});

export { coursesRouter };
