import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

const paramsSchema = z.object({
  commentId: z.string(),
})

const bodySchema = z.object({
  content: z.string().min(1).max(1000),
})

export const replyLessonComment = new Hono()
  .post('/comment/:commentId/reply', validator('param', paramsSchema), validator('json', bodySchema), async (c) => {
    try {
      const { commentId } = c.req.valid('param')
      const { content } = c.req.valid('json')

      // Implementação da inserção no banco de dados será adicionada posteriormente
      const newReply = {
        id: Date.now().toString(),
        content,
        commentId,
        userId: 'current-user-id', // Obter do contexto de autenticação
        createdAt: new Date().toISOString(),
        user: {
          id: 'current-user-id',
          name: 'Current User',
          email: '<EMAIL>',
        },
      }

      return c.json({ reply: newReply })
    } catch (error) {
      return c.json({ error: 'Failed to reply to comment' }, 500)
    }
  })
