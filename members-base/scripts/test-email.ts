import { logger } from "../packages/logs";
import { sendEmail } from "../packages/mail";



async function testEmail() {
  try {
    logger.info("🧪 Testando envio de email com Mailgun...");

    const result = await sendEmail({
      to: "<EMAIL>", // Substitua por um email válido para teste
      subject: "Teste de Email - Cakto Members",
      text: "Este é um teste do sistema de email configurado com Mailgun.",
      html: `
        <h1>Teste de Email</h1>
        <p>Este é um teste do sistema de email configurado com <strong>Mailgun</strong>.</p>
        <p>Se você recebeu este email, significa que a configuração está funcionando corretamente!</p>
        <p>Data/Hora: ${new Date().toLocaleString('pt-BR')}</p>
      `
    });

    if (result) {
      logger.info("✅ Email enviado com sucesso!");
    } else {
      logger.error("❌ Falha ao enviar email");
    }
  } catch (error) {
    logger.error("❌ Erro ao testar email:", error);
  }
}

testEmail();
