#!/usr/bin/env tsx

import { auth } from "../packages/auth";
import { logger } from "@repo/logs";

async function testMagicLinkFlow() {
  try {
    console.log("🧪 Testing fixed magic link flow...\n");

    const testEmail = "<EMAIL>";
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const callbackURL = `${baseUrl}/app`;

    console.log(`📧 Testing magic link for: ${testEmail}`);
    console.log(`🔗 Callback URL: ${callbackURL}`);
    console.log(`🌐 Base URL: ${baseUrl}\n`);

    // Test magic link generation
    console.log("1. 📤 Generating magic link...");
    
    const result = await auth.api.signInMagicLink({
      body: {
        email: testEmail,
        callbackURL: callbackURL,
      },
      headers: new Headers(),
    });

    if (result.error) {
      console.error("❌ Error generating magic link:", result.error);
      return;
    }

    console.log("✅ Magic link generated successfully!");
    console.log("📋 Expected flow:");
    console.log("   1. User receives email with magic link");
    console.log("   2. User clicks link in email");
    console.log("   3. Better Auth verifies token at /api/auth/magic-link/verify");
    console.log("   4. User is authenticated and redirected to callback URL");
    console.log("   5. No custom callback page interference");

    console.log("\n🔍 Configuration check:");
    console.log("   - Magic link plugin: ✅ Enabled");
    console.log("   - Custom callback page: ✅ Removed");
    console.log("   - Better Auth handler: ✅ Available at /api/auth/*");
    console.log("   - Redirect configuration: ✅ Added to onSuccess");

    console.log("\n📧 Email configuration:");
    const smtpConfigured = process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS;
    console.log(`   - SMTP configured: ${smtpConfigured ? "✅" : "❌"}`);
    
    if (!smtpConfigured) {
      console.log("   ⚠️  To test email sending, configure SMTP environment variables:");
      console.log("      - SMTP_HOST");
      console.log("      - SMTP_USER");
      console.log("      - SMTP_PASS");
    }

    console.log("\n🎯 Next steps to test:");
    console.log("   1. Configure SMTP settings in environment variables");
    console.log("   2. Start the development server: npm run dev");
    console.log("   3. Try magic link authentication from the login page");
    console.log("   4. Check email for magic link");
    console.log("   5. Click the link and verify redirect to /app");

  } catch (error) {
    console.error("❌ Error testing magic link flow:", error);
  }
}

async function testSSOConfiguration() {
  try {
    console.log("\n🔐 Testing SSO configuration...\n");

    console.log("1. 🔧 Checking SSO plugin configuration:");
    
    // Check if environment variables are set
    const clientId = process.env.CAKTO_CLIENT_ID;
    const clientSecret = process.env.CAKTO_CLIENT_SECRET;
    
    console.log(`   - CAKTO_CLIENT_ID: ${clientId ? "✅ Set" : "❌ Missing"}`);
    console.log(`   - CAKTO_CLIENT_SECRET: ${clientSecret ? "✅ Set" : "❌ Missing"}`);

    console.log("\n2. 🌐 SSO Provider Configuration:");
    console.log("   - Provider ID: django-sso");
    console.log("   - Discovery URL: https://sso.cakto.com.br/oauth/.well-known/openid-configuration");
    console.log("   - Scopes: openid, user");
    console.log("   - PKCE: ✅ Enabled");
    console.log("   - Response Type: code");

    console.log("\n3. 🔄 Expected SSO Flow:");
    console.log("   1. User clicks SSO login button");
    console.log("   2. Frontend calls authClient.signIn.oauth2({ providerId: 'django-sso' })");
    console.log("   3. User is redirected to Cakto SSO");
    console.log("   4. User authenticates with Cakto");
    console.log("   5. Cakto redirects back to /api/auth/oauth2/callback/django-sso");
    console.log("   6. Better Auth processes the callback and creates session");
    console.log("   7. User is redirected to the app");

    if (clientId && clientSecret) {
      console.log("\n✅ SSO is properly configured and ready to use!");
    } else {
      console.log("\n⚠️  SSO needs environment variables to be configured:");
      console.log("   Add to your .env.local file:");
      console.log("   CAKTO_CLIENT_ID=your_client_id");
      console.log("   CAKTO_CLIENT_SECRET=your_client_secret");
    }

  } catch (error) {
    console.error("❌ Error testing SSO configuration:", error);
  }
}

// Run both tests
async function runAllTests() {
  await testMagicLinkFlow();
  await testSSOConfiguration();
  
  console.log("\n🎉 All authentication tests completed!");
  console.log("\n📝 Summary of fixes applied:");
  console.log("   ✅ Added genericOAuth plugin for SSO");
  console.log("   ✅ Configured Cakto SSO provider");
  console.log("   ✅ Fixed frontend auth client baseURL");
  console.log("   ✅ Removed custom magic link callback page");
  console.log("   ✅ Added proper redirect handling in onSuccess");
  console.log("   ✅ Added environment variables to .env.local.example");
}

runAllTests()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Tests failed:", error);
    process.exit(1);
  });
