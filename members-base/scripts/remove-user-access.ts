import { getUserByEmail, db } from "@repo/database";
import { logger } from "@repo/logs";

async function removeUserAccess() {
  try {
    const email = "<EMAIL>";

    logger.info(`🧹 Removendo acesso do usuário ${email}...`);

    // Buscar o usuário
    const user = await getUserByEmail(email);

    if (!user) {
      logger.info(`👤 Usuário ${email} não encontrado`);
      return;
    }

    logger.info(`👤 Usuário encontrado: ${user.name} (${user.email})`);

    // Buscar o curso de teste
    const testCourse = await db.courses.findFirst({
      where: { name: "Curso de Teste Magic Link" }
    });

    if (testCourse) {
      // Remover acesso ao curso
      const deletedAccess = await db.userCourses.deleteMany({
        where: {
          userId: user.id,
          courseId: testCourse.id,
        },
      });

      logger.info(`🗑️ Removido ${deletedAccess.count} acesso(es) ao curso ${testCourse.name}`);
    }

    // Remover da organização
    const deletedMembership = await db.member.deleteMany({
      where: {
        userId: user.id,
      },
    });

    logger.info(`👥 Removido ${deletedMembership.count} membro(s) da organização`);

    // Remover o usuário
    const deletedUser = await db.user.delete({
      where: { id: user.id },
    });

    logger.info(`🗑️ Usuário ${deletedUser.email} removido com sucesso`);

    // Remover o curso de teste também
    if (testCourse) {
      await db.courses.delete({
        where: { id: testCourse.id },
      });
      logger.info(`📚 Curso de teste removido`);
    }

    logger.info(`✅ Limpeza concluída!`);

  } catch (error) {
    logger.error("❌ Erro na limpeza:", error);
  }
}

// Executar a limpeza
removeUserAccess().then(() => {
  logger.info("🏁 Limpeza finalizada!");
  process.exit(0);
}).catch((error) => {
  logger.error("💥 Erro fatal:", error);
  process.exit(1);
});
