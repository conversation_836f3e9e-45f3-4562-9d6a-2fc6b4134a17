#!/usr/bin/env tsx

import { auth } from "../packages/auth";
import { logger } from "@repo/logs";

async function testAuthConfiguration() {
  try {
    console.log("🧪 Testing authentication configuration...\n");

    // Test 1: Check if Better Auth instance is properly configured
    console.log("1. ✅ Better Auth instance created successfully");
    console.log(`   - App Name: ${auth.options.appName}`);
    console.log(`   - Base URL: ${auth.options.baseURL}`);
    console.log(`   - Trusted Origins: ${auth.options.trustedOrigins?.join(", ")}`);

    // Test 2: Check plugins
    console.log("\n2. 📦 Checking plugins:");
    const plugins = auth.options.plugins || [];
    const pluginNames = plugins.map((plugin: any) => plugin.id || plugin.name || "unknown");
    console.log(`   - Loaded plugins: ${pluginNames.join(", ")}`);
    
    // Check for specific plugins
    const hasGenericOAuth = pluginNames.includes("generic-oauth");
    const hasMagicLink = pluginNames.includes("magic-link");
    const hasOrganization = pluginNames.includes("organization");
    
    console.log(`   - Generic OAuth (SSO): ${hasGenericOAuth ? "✅" : "❌"}`);
    console.log(`   - Magic Link: ${hasMagicLink ? "✅" : "❌"}`);
    console.log(`   - Organization: ${hasOrganization ? "✅" : "❌"}`);

    // Test 3: Check environment variables for SSO
    console.log("\n3. 🔐 Checking SSO environment variables:");
    const caktoClientId = process.env.CAKTO_CLIENT_ID;
    const caktoClientSecret = process.env.CAKTO_CLIENT_SECRET;
    
    console.log(`   - CAKTO_CLIENT_ID: ${caktoClientId ? "✅ Set" : "❌ Missing"}`);
    console.log(`   - CAKTO_CLIENT_SECRET: ${caktoClientSecret ? "✅ Set" : "❌ Missing"}`);

    // Test 4: Check email configuration
    console.log("\n4. 📧 Checking email configuration:");
    const smtpHost = process.env.SMTP_HOST;
    const smtpUser = process.env.SMTP_USER;
    const smtpPass = process.env.SMTP_PASS;
    
    console.log(`   - SMTP_HOST: ${smtpHost ? "✅ Set" : "❌ Missing"}`);
    console.log(`   - SMTP_USER: ${smtpUser ? "✅ Set" : "❌ Missing"}`);
    console.log(`   - SMTP_PASS: ${smtpPass ? "✅ Set" : "❌ Missing"}`);

    // Test 5: Check database configuration
    console.log("\n5. 🗄️ Checking database configuration:");
    const databaseUrl = process.env.DATABASE_URL;
    console.log(`   - DATABASE_URL: ${databaseUrl ? "✅ Set" : "❌ Missing"}`);

    // Summary
    console.log("\n📋 Configuration Summary:");
    const ssoReady = hasGenericOAuth && caktoClientId && caktoClientSecret;
    const magicLinkReady = hasMagicLink && smtpHost && smtpUser && smtpPass;
    
    console.log(`   - SSO Authentication: ${ssoReady ? "✅ Ready" : "❌ Needs configuration"}`);
    console.log(`   - Magic Link Authentication: ${magicLinkReady ? "✅ Ready" : "❌ Needs configuration"}`);
    console.log(`   - Database: ${databaseUrl ? "✅ Ready" : "❌ Needs configuration"}`);

    if (ssoReady && magicLinkReady && databaseUrl) {
      console.log("\n🎉 All authentication methods are properly configured!");
    } else {
      console.log("\n⚠️  Some authentication methods need configuration. Check the missing environment variables above.");
    }

    // Test 6: Test API endpoints availability
    console.log("\n6. 🔗 Testing API endpoints:");
    try {
      // Test if auth handler is available
      const testRequest = new Request("http://localhost:3000/api/auth/session", {
        method: "GET",
        headers: { "Content-Type": "application/json" }
      });
      
      console.log("   - Auth handler: ✅ Available");
    } catch (error) {
      console.log("   - Auth handler: ❌ Error creating test request");
    }

  } catch (error) {
    console.error("❌ Error testing authentication configuration:", error);
    process.exit(1);
  }
}

// Run the test
testAuthConfiguration()
  .then(() => {
    console.log("\n✅ Authentication configuration test completed!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
