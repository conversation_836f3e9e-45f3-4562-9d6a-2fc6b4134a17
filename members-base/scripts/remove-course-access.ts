import { db } from "../packages/database";
import { logger } from "../packages/logs";

async function removeCourseAccess() {
  try {
    const email = "<EMAIL>";
    const courseProductId = "ff3fdf61-e88f-43b5-982a-32d50f112414";

    logger.info(`🗑️ Removendo acesso do usuário ${email} ao curso...`);

    // Buscar o usuário
    const user = await db.user.findUnique({
      where: { email }
    });

    if (!user) {
      logger.error(`❌ Usuário ${email} não encontrado`);
      return;
    }

    // Buscar a associação do produto
    const courseProduct = await db.courseProduct.findFirst({
      where: {
        caktoProductId: courseProductId
      }
    });

    if (!courseProduct) {
      logger.error(`❌ Produto ${courseProductId} não encontrado`);
      return;
    }

    // Remover acesso ao curso
    const deletedAccess = await db.userCourses.deleteMany({
      where: {
        userId: user.id,
        courseId: courseProduct.courseId
      }
    });

    if (deletedAccess.count > 0) {
      logger.info(`✅ Acesso removido: ${deletedAccess.count} registro(s) deletado(s)`);
      logger.info(`   Usuário: ${user.name} (${user.email})`);
      logger.info(`   Curso: ${courseProductId}`);
    } else {
      logger.info(`ℹ️ Usuário não tinha acesso ao curso`);
    }

  } catch (error) {
    logger.error("❌ Erro ao remover acesso:", error);
  } finally {
    await db.$disconnect();
  }
}

removeCourseAccess();
