#!/usr/bin/env tsx

import { auth } from "../packages/auth";

async function testMagicLinkFlow() {
  try {
    console.log("🧪 Testando fluxo de magic link...");

    const testEmail = "<EMAIL>";
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

    console.log(`📧 Enviando magic link para ${testEmail}...`);

    // Enviar magic link
    const result = await auth.api.signInMagicLink({
      body: {
        email: testEmail,
        callbackURL: `${baseUrl}/app`,
      },
      headers: new Headers(),
    });

    console.log("✅ Magic link enviado com sucesso!");
    console.log("📋 Próximos passos:");
    console.log("1. Verifique o email em " + testEmail);
    console.log("2. Clique no link no email");
    console.log("3. Verifique se você é redirecionado para /app");
    console.log("4. Verifique se está autenticado");

  } catch (error) {
    console.error("❌ Erro no teste de magic link:", error);
  }
}

testMagicLinkFlow();
