#!/usr/bin/env tsx

import { auth } from "../packages/auth";
import { getBaseUrl } from "@repo/utils";

async function diagnoseProductionAuth() {
  console.log("🔍 Diagnosticando problemas de autenticação em produção...\n");

  // 1. Verificar configurações básicas
  console.log("1. 📋 Configurações Básicas:");
  console.log(`   - NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   - Base URL: ${getBaseUrl()}`);
  console.log(`   - NEXT_PUBLIC_SITE_URL: ${process.env.NEXT_PUBLIC_SITE_URL}`);
  console.log(`   - Better Auth Secret: ${process.env.BETTER_AUTH_SECRET ? "✅ Definido" : "❌ Faltando"}`);

  // 2. Verificar configuração do Better Auth
  console.log("\n2. 🔐 Configuração Better Auth:");
  console.log(`   - App Name: ${auth.options.appName}`);
  console.log(`   - Base URL: ${auth.options.baseURL}`);
  console.log(`   - Trusted Origins: ${auth.options.trustedOrigins?.join(", ")}`);

  // 3. Verificar plugins habilitados
  console.log("\n3. 🔌 Plugins Habilitados:");
  const plugins = auth.options.plugins || [];
  const pluginNames = plugins.map((plugin: any) => plugin.id || plugin.name || "unknown");
  console.log(`   - Plugins: ${pluginNames.join(", ")}`);
  
  const hasEmailPassword = auth.options.emailAndPassword?.enabled;
  const hasMagicLink = pluginNames.includes("magic-link");
  const hasGenericOAuth = pluginNames.includes("generic-oauth");
  
  console.log(`   - Email/Password: ${hasEmailPassword ? "✅" : "❌"}`);
  console.log(`   - Magic Link: ${hasMagicLink ? "✅" : "❌"}`);
  console.log(`   - SSO (OAuth): ${hasGenericOAuth ? "✅" : "❌"}`);

  // 4. Verificar configuração de email
  console.log("\n4. 📧 Configuração de Email:");
  const smtpHost = process.env.SMTP_HOST;
  const smtpUser = process.env.SMTP_USER;
  const smtpPass = process.env.SMTP_PASS;
  const smtpPort = process.env.SMTP_PORT;
  
  console.log(`   - SMTP_HOST: ${smtpHost || "❌ Faltando"}`);
  console.log(`   - SMTP_USER: ${smtpUser || "❌ Faltando"}`);
  console.log(`   - SMTP_PASS: ${smtpPass ? "✅ Definido" : "❌ Faltando"}`);
  console.log(`   - SMTP_PORT: ${smtpPort || "587 (padrão)"}`);

  // 5. Verificar configuração SSO
  console.log("\n5. 🔗 Configuração SSO:");
  const caktoClientId = process.env.CAKTO_CLIENT_ID;
  const caktoClientSecret = process.env.CAKTO_CLIENT_SECRET;
  const caktoApiUrl = process.env.CAKTO_API_URL;
  
  console.log(`   - CAKTO_CLIENT_ID: ${caktoClientId || "❌ Faltando"}`);
  console.log(`   - CAKTO_CLIENT_SECRET: ${caktoClientSecret ? "✅ Definido" : "❌ Faltando"}`);
  console.log(`   - CAKTO_API_URL: ${caktoApiUrl || "❌ Faltando"}`);

  // 6. Verificar banco de dados
  console.log("\n6. 🗄️ Configuração do Banco:");
  const databaseUrl = process.env.DATABASE_URL;
  console.log(`   - DATABASE_URL: ${databaseUrl ? "✅ Definido" : "❌ Faltando"}`);

  // 7. Testar endpoints críticos
  console.log("\n7. 🎯 Testando Endpoints Críticos:");
  
  try {
    // Teste básico de configuração
    const baseUrl = getBaseUrl();
    console.log(`   - Base URL resolvida: ${baseUrl}`);
    
    // Verificar se as URLs estão corretas
    const expectedUrls = {
      login: `${baseUrl}/api/auth/sign-in/email`,
      magicLink: `${baseUrl}/api/auth/magic-link/verify`,
      sso: `${baseUrl}/api/auth/oauth2/django-sso`,
      session: `${baseUrl}/api/auth/session`
    };
    
    console.log("   - URLs dos endpoints:");
    Object.entries(expectedUrls).forEach(([name, url]) => {
      console.log(`     • ${name}: ${url}`);
    });

  } catch (error) {
    console.log(`   ❌ Erro ao testar endpoints: ${error}`);
  }

  // 8. Verificar problemas comuns
  console.log("\n8. ⚠️ Verificação de Problemas Comuns:");
  
  const issues = [];
  
  if (!process.env.BETTER_AUTH_SECRET) {
    issues.push("BETTER_AUTH_SECRET não definido");
  }
  
  if (!smtpHost || !smtpUser || !smtpPass) {
    issues.push("Configuração SMTP incompleta");
  }
  
  if (!databaseUrl) {
    issues.push("DATABASE_URL não definido");
  }
  
  if (process.env.NODE_ENV === "production" && getBaseUrl().includes("localhost")) {
    issues.push("Base URL ainda aponta para localhost em produção");
  }
  
  if (!hasEmailPassword) {
    issues.push("Login com email/senha não habilitado");
  }
  
  if (!hasMagicLink) {
    issues.push("Magic Link não configurado");
  }
  
  if (issues.length === 0) {
    console.log("   ✅ Nenhum problema comum detectado!");
  } else {
    console.log("   ❌ Problemas encontrados:");
    issues.forEach(issue => console.log(`     • ${issue}`));
  }

  // 9. Recomendações
  console.log("\n9. 💡 Recomendações para Produção:");
  console.log("   □ Verificar se todas as variáveis de ambiente estão definidas no container");
  console.log("   □ Confirmar que o banco de dados está acessível");
  console.log("   □ Testar conectividade SMTP");
  console.log("   □ Verificar logs do container para erros específicos");
  console.log("   □ Confirmar que o domínio SSL está funcionando");
  console.log("   □ Testar endpoints individualmente com curl/Postman");

  // 10. Comandos de teste
  console.log("\n10. 🧪 Comandos de Teste:");
  const baseUrl = getBaseUrl();
  console.log(`   # Testar sessão atual:`);
  console.log(`   curl -X GET "${baseUrl}/api/auth/session" -H "Cookie: better-auth.session_token=TOKEN"`);
  console.log(`   
   # Testar login com email:`);
  console.log(`   curl -X POST "${baseUrl}/api/auth/sign-in/email" \\
     -H "Content-Type: application/json" \\
     -d '{"email":"<EMAIL>","password":"password"}'`);
  console.log(`   
   # Testar magic link:`);
  console.log(`   curl -X POST "${baseUrl}/api/auth/sign-in/magic-link" \\
     -H "Content-Type: application/json" \\
     -d '{"email":"<EMAIL>","callbackURL":"/app"}'`);

  console.log("\n✅ Diagnóstico completo!");
}

// Executar diagnóstico
diagnoseProductionAuth()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Erro no diagnóstico:", error);
    process.exit(1);
  });
