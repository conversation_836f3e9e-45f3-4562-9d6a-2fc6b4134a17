# 🐳 Docker Deployment Guide

Este guia mostra como fazer deploy da aplicação Cakto Members usando Docker em diferentes plataformas.

## 📋 Pré-requisitos

- Docker instalado
- Variáveis de ambiente configuradas (veja `env-production.txt`)
- Acesso às plataformas de deploy escolhidas

## 🚀 Build Local

### Build básico
```bash
# Build para arquitetura local
./scripts/build-docker.sh

# Build para múltiplas arquiteturas
./scripts/build-docker.sh linux/amd64,linux/arm64 cakto-members:latest
```

### Teste local
```bash
# Usando docker-compose (recomendado)
docker-compose up --build

# Usando Docker diretamente
docker run -p 3000:3000 --env-file .env cakto-members:latest
```

## ☁️ Deploy em Plataformas

### Google Cloud Run

1. **Configure o Google Cloud CLI:**
```bash
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

2. **Build e push da imagem:**
```bash
# Build para Google Cloud
./scripts/build-docker.sh linux/amd64 cakto-members:latest

# Tag para Google Container Registry
docker tag cakto-members:latest gcr.io/YOUR_PROJECT_ID/cakto-members:latest

# Push para GCR
docker push gcr.io/YOUR_PROJECT_ID/cakto-members:latest
```

3. **Deploy no Cloud Run:**
```bash
gcloud run deploy cakto-members \
  --image gcr.io/YOUR_PROJECT_ID/cakto-members:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NODE_ENV=production \
  --set-env-vars DATABASE_URL=YOUR_DATABASE_URL \
  --set-env-vars BETTER_AUTH_SECRET=YOUR_SECRET
```

### AWS ECS (Elastic Container Service)

1. **Configure o AWS CLI:**
```bash
aws configure
```

2. **Crie um repositório ECR:**
```bash
aws ecr create-repository --repository-name cakto-members
```

3. **Build e push:**
```bash
# Login no ECR
aws ecr get-login-password --region YOUR_REGION | docker login --username AWS --password-stdin YOUR_ACCOUNT_ID.dkr.ecr.YOUR_REGION.amazonaws.com

# Build e tag
./scripts/build-docker.sh linux/amd64 cakto-members:latest
docker tag cakto-members:latest YOUR_ACCOUNT_ID.dkr.ecr.YOUR_REGION.amazonaws.com/cakto-members:latest

# Push
docker push YOUR_ACCOUNT_ID.dkr.ecr.YOUR_REGION.amazonaws.com/cakto-members:latest
```

4. **Deploy via AWS Console ou CLI**

### Digital Ocean App Platform

1. **Via Dashboard:**
   - Acesse [Digital Ocean App Platform](https://cloud.digitalocean.com/apps)
   - Clique em "Create App"
   - Escolha "Docker Hub" ou "GitHub"
   - Configure as variáveis de ambiente

2. **Via CLI (doctl):**
```bash
# Instale o doctl
# Configure as variáveis de ambiente no dashboard
# Use o docker-compose.yml como referência
```

### Coolify

1. **Configure o Coolify:**
   - Acesse seu servidor Coolify
   - Crie um novo projeto
   - Escolha "Docker Compose"

2. **Use o docker-compose.yml:**
```yaml
# O arquivo docker-compose.yml já está configurado
# Apenas ajuste as variáveis de ambiente no dashboard do Coolify
```

## 🔧 Configuração de Variáveis de Ambiente

### Variáveis Obrigatórias

```bash
# Database
DATABASE_URL=postgres://...

# Better Auth
BETTER_AUTH_SECRET=your-secret-here

# URLs
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Mail
MAIL__ADDRESS_FROM=<EMAIL>
MAIL_HOST=smtp.yourprovider.com
MAIL_PORT=587
MAIL_USER=your-email
MAIL_PASS=your-password

# Storage
S3_ENDPOINT=https://your-storage-endpoint
S3_REGION=your-region
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key
S3_BUCKET_NAME=your-bucket
```

### Variáveis Opcionais

```bash
# SSO (se usar)
CAKTO_SSO_CLIENT_ID=your-client-id
CAKTO_SSO_CLIENT_SECRET=your-client-secret
SSO_REDIRECT_URI=https://your-domain.com/auth/callback

# Bunny.net (se usar)
NEXT_PUBLIC_BUNNY_LIBRARY_ID=your-library-id
NEXT_PUBLIC_BUNNY_SIGNATURE=your-signature
NEXT_PUBLIC_BUNNY_EXPIRE=your-expire-timestamp
```

## 🏥 Health Check

A aplicação inclui um endpoint de health check:

```bash
curl http://localhost:3000/api/health
```

## 📊 Monitoramento

### Logs
```bash
# Docker logs
docker logs container-name

# Docker Compose logs
docker-compose logs -f web
```

### Métricas
- Configure monitoramento específico da plataforma
- Use ferramentas como Prometheus + Grafana
- Configure alertas para downtime

## 🔒 Segurança

### Boas Práticas
1. **Nunca commite variáveis de ambiente** no código
2. **Use secrets management** da plataforma
3. **Configure HTTPS** em produção
4. **Use imagens base seguras** (já configurado)
5. **Execute como usuário não-root** (já configurado)

### Network Security
```bash
# Exponha apenas a porta necessária
EXPOSE 3000

# Use reverse proxy para SSL termination
# Configure firewall rules
```

## 🚨 Troubleshooting

### Problemas Comuns

1. **Build falha:**
```bash
# Limpe cache
docker system prune -a
# Rebuild sem cache
docker build --no-cache -f apps/web/Dockerfile .
```

2. **Aplicação não inicia:**
```bash
# Verifique logs
docker logs container-name
# Verifique variáveis de ambiente
docker exec container-name env
```

3. **Problemas de conectividade:**
```bash
# Teste conectividade
docker exec container-name ping google.com
# Verifique DNS
docker exec container-name nslookup your-database-host
```

### Suporte

Para problemas específicos:
1. Verifique os logs da aplicação
2. Consulte a documentação da plataforma
3. Verifique as variáveis de ambiente
4. Teste localmente primeiro

## 📚 Recursos Adicionais

- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Next.js Docker Guide](https://nextjs.org/docs/deployment#docker-image)
