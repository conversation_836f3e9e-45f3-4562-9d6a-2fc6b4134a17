# =============================================================================
# MEMBERS-BASE - CONFIGURAÇÃO LOCAL DE EXEMPLO
# =============================================================================
# Copie este arquivo para .env.local e configure as variáveis conforme necessário
# cp .env.local.example .env.local

# =============================================================================
# DATABASE
# =============================================================================
# URL de conexão com o banco de dados PostgreSQL
# Formato: postgresql://usuario:senha@host:porta/nome_do_banco
DATABASE_URL="postgresql://usuario:senha@localhost:5432/nome_do_banco"

# =============================================================================
# BETTER AUTH
# =============================================================================
# Secret para criptografia de sessões (obrigatório)
# Gere um secret aleatório usando: openssl rand -hex 64
BETTER_AUTH_SECRET="sua_chave_secreta_aqui_64_caracteres_hexadecimais"

# =============================================================================
# NEXT.JS
# =============================================================================
# URL base do site (usado para callbacks e redirecionamentos)
# DESENVOLVIMENTO: http://localhost:3000
# PRODUÇÃO: https://caktomembers.cloud.cakto.app (ou seu domínio)
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
# Porta onde o servidor Next.js irá rodar
PORT=3000

# =============================================================================
# AUTENTICAÇÃO SOCIAL (OPCIONAL)
# =============================================================================
# Google OAuth - Configure se quiser login com Google
# Obtenha as credenciais em: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# =============================================================================
# CAKTO SSO (OBRIGATÓRIO PARA INTEGRAÇÃO)
# =============================================================================
# URL da API do Cakto para autenticação SSO
CAKTO_API_URL="https://sso.cakto.com.br"
# Client ID fornecido pelo Cakto para sua aplicação
CAKTO_CLIENT_ID="seu_client_id_aqui"
# Client Secret fornecido pelo Cakto para sua aplicação
CAKTO_CLIENT_SECRET="seu_client_secret_aqui"

# URI de redirecionamento após autenticação SSO
SSO_REDIRECT_URI="http://localhost:3000/auth/callback"
# Domínio do SSO (usado para validação de cookies)
SSO_DOMAIN="sso.cakto.com.br"
# Domínio principal do Cakto (usado para cookies compartilhados)
CAKTO_DOMAIN=".cakto.com.br"

# =============================================================================
# CONFIGURAÇÃO DE EMAIL
# =============================================================================
# Endereço de email remetente padrão
MAIL__ADDRESS_FROM="<EMAIL>"
# Habilitar envio de emails via SMTP
MAIL__SMTP_ENABLE=true
# Servidor SMTP
MAIL_HOST="smtp.seudominio.com.br"
# Porta do servidor SMTP (587 para TLS, 465 para SSL)
MAIL_PORT=587
# Usar conexão segura (true para SSL, false para TLS)
MAILER_SMTP_SECURE=false
# URL do servidor SMTP (opcional)
MAILER_SMTP_URL=""
# Usuário do servidor SMTP
MAIL_USER="<EMAIL>"
# Senha do servidor SMTP
MAIL_PASS="sua_senha_smtp_aqui"
# Aceitar certificados SSL auto-assinados (apenas para desenvolvimento)
MAIL__TLS_REJECT_UNAUTHORIZED=false
# Usar configurações customizadas de email
MAIL_USE_CUSTOM_CONFIGS=true

# =============================================================================
# STORAGE (DIGITALOCEAN SPACES / S3)
# =============================================================================
# Endpoint do DigitalOcean Spaces (ou AWS S3)
S3_ENDPOINT="https://seu-bucket.nyc3.digitaloceanspaces.com/"
# Região do bucket (ex: nyc3, sgp1, fra1)
S3_REGION="nyc3"
# Access Key ID do DigitalOcean Spaces
S3_ACCESS_KEY_ID="sua_access_key_aqui"
# Secret Access Key do DigitalOcean Spaces
S3_SECRET_ACCESS_KEY="sua_secret_key_aqui"
# Nome do bucket para avatares (visível no frontend)
NEXT_PUBLIC_AVATARS_BUCKET_NAME="seu-bucket-avatares"
# Nome do bucket principal (para uso interno)
S3_BUCKET_NAME="seu-bucket-principal"

# =============================================================================
# BUNNY.NET STREAM (OPCIONAL - PARA UPLOAD DE VÍDEOS)
# =============================================================================
# ID da biblioteca no Bunny.net
NEXT_PUBLIC_BUNNY_LIBRARY_ID="seu_library_id_aqui"
# Chave de assinatura para upload seguro
NEXT_PUBLIC_BUNNY_SIGNATURE="sua_signature_aqui"
# Timestamp de expiração da assinatura
NEXT_PUBLIC_BUNNY_EXPIRE="1735689600"

# =============================================================================
# ANALYTICS (OPCIONAL)
# =============================================================================
# Google Analytics - ID de rastreamento
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""
# PostHog - Chave da API para analytics
NEXT_PUBLIC_POSTHOG_KEY=""

# =============================================================================
# AMBIENTE
# =============================================================================
# Ambiente de execução (development, production, test)
NODE_ENV="development"
# URL do backend do Cakto (usado para integrações)
NEXT_PUBLIC_CAKTO_BACKEND_URL="http://localhost:3000"

# =============================================================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# =============================================================================
# Usar dados mock do Cakto para testes (true/false)
USE_CAKTO_MOCK=false

# =============================================================================
# CONFIGURAÇÕES ADICIONAIS
# =============================================================================
# Adicione outras variáveis de ambiente específicas do seu projeto aqui
# Exemplo:
# REDIS_URL="redis://localhost:6379"
# SENTRY_DSN="sua_sentry_dsn_aqui"
